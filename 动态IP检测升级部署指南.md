# 动态IP检测升级部署指南

## 📋 概述

本指南详细说明如何部署和使用升级后的动态IP检测方案，包括配置、测试、监控和故障排除。

## 🚀 快速开始

### 1. 前置条件检查

```bash
# 检查必需工具
command -v dig >/dev/null || echo "需要安装 dig (bind-utils)"
command -v ping >/dev/null || echo "需要安装 ping (iputils-ping)"
command -v curl >/dev/null || echo "需要安装 curl"
command -v jq >/dev/null || echo "需要安装 jq"
command -v bc >/dev/null || echo "需要安装 bc"

# 检查网络连通性
ping -c 1 ******* && echo "Cloudflare DNS 可达"
ping -c 1 ******* && echo "Google DNS 可达"  
ping -c 1 ************ && echo "腾讯DNSPod 可达"
```

### 2. 配置DNS记录

在您的DNS提供商（如Cloudflare）中配置：

```
# A记录配置
ip.example.com    A    YOUR_CURRENT_PUBLIC_IP    TTL=60
```

### 3. 设置环境变量

```bash
export DOMAIN="example.com"
export CLOUDFLARE_API_TOKEN="your-api-token"
export CLOUDFLARE_ZONE_ID="your-zone-id"  # 可选
```

### 4. 运行测试

```bash
# 给脚本执行权限
chmod +x scripts/dynamic-ip-manager.sh
chmod +x scripts/test-ip-detection.sh

# 运行功能测试
./scripts/test-ip-detection.sh $DOMAIN

# 单次IP检测测试
./scripts/dynamic-ip-manager.sh detect --domain $DOMAIN
```

## 🔧 详细配置

### 1. 脚本配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--domain` | 必需 | 主域名 |
| `--internal-port` | 8443 | 内部服务器端口 |
| `--check-interval` | 10 | 检测间隔（秒） |
| `--external-namespace` | matrix-external | 外部服务命名空间 |
| `--internal-namespace` | matrix-internal | 内部服务命名空间 |

### 2. 环境变量配置

```bash
# Cloudflare API配置
export CLOUDFLARE_API_TOKEN="your-token"
export CLOUDFLARE_ZONE_ID="your-zone-id"

# 检测配置
export CHECK_INTERVAL="10"

# 日志级别
export LOG_LEVEL="info"
```

### 3. DNS服务器配置

脚本使用以下DNS服务器顺序：

```bash
1. ******* (Cloudflare) - 主DNS服务器
2. ******* (Google) - 备用DNS服务器1  
3. ************ (腾讯DNSPod) - 备用DNS服务器2
```

## 📊 使用方法

### 1. 基本操作

```bash
# 检测当前IP
./scripts/dynamic-ip-manager.sh detect --domain example.com

# 执行一次IP更新检查
./scripts/dynamic-ip-manager.sh update --domain example.com

# 启动持续监控（10秒间隔）
./scripts/dynamic-ip-manager.sh monitor --domain example.com

# 初始化动态IP管理
./scripts/dynamic-ip-manager.sh setup --domain example.com
```

### 2. 高级用法

```bash
# 自定义检测间隔（30秒）
./scripts/dynamic-ip-manager.sh monitor \
  --domain example.com \
  --check-interval 30

# 指定自定义端口
./scripts/dynamic-ip-manager.sh monitor \
  --domain example.com \
  --internal-port 9443

# 指定自定义命名空间
./scripts/dynamic-ip-manager.sh monitor \
  --domain example.com \
  --external-namespace my-external \
  --internal-namespace my-internal
```

### 3. Kubernetes部署

```bash
# 创建配置
kubectl apply -f scripts/dynamic-ip-config-example.yaml

# 检查部署状态
kubectl get pods -n matrix-external -l app.kubernetes.io/name=dynamic-ip-manager

# 查看日志
kubectl logs -n matrix-external -l app.kubernetes.io/name=dynamic-ip-manager -f

# 手动触发IP检测
kubectl exec -n matrix-external deployment/dynamic-ip-manager -- \
  /usr/local/bin/dynamic-ip-manager.sh detect --domain example.com
```

## 🔍 监控和诊断

### 1. 日志监控

```bash
# 实时查看日志
kubectl logs -n matrix-external -l app.kubernetes.io/name=dynamic-ip-manager -f

# 查看最近的日志
kubectl logs -n matrix-external -l app.kubernetes.io/name=dynamic-ip-manager --tail=100

# 搜索特定事件
kubectl logs -n matrix-external -l app.kubernetes.io/name=dynamic-ip-manager | grep "IP变化"
```

### 2. 状态检查

```bash
# 检查ConfigMap中存储的当前IP
kubectl get configmap dynamic-ip-config -n matrix-external -o yaml

# 检查DNS记录
dig +short ip.example.com @*******
dig +short matrix.example.com @*******

# 检查服务可访问性
curl -s https://example.com/.well-known/matrix/server
```

### 3. 性能监控

```bash
# 运行性能测试
./scripts/test-ip-detection.sh example.com

# 监控系统资源使用
kubectl top pods -n matrix-external -l app.kubernetes.io/name=dynamic-ip-manager
```

## 🚨 故障排除

### 1. 常见问题

#### 问题：IP检测失败
```bash
# 检查DNS记录配置
dig +short ip.example.com

# 检查DNS服务器可达性
ping -c 1 *******
ping -c 1 *******
ping -c 1 ************

# 检查网络连接
curl -s https://ipv4.icanhazip.com
```

#### 问题：DNS更新失败
```bash
# 检查Cloudflare API Token
curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
  -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN"

# 检查Zone ID
curl -X GET "https://api.cloudflare.com/client/v4/zones?name=example.com" \
  -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN"
```

#### 问题：服务验证失败
```bash
# 检查内部服务状态
kubectl get pods -n matrix-internal

# 检查服务端点
curl -s https://matrix.example.com:8443/_matrix/client/versions
```

### 2. 调试模式

```bash
# 启用详细日志
export LOG_LEVEL="debug"

# 手动执行检测步骤
./scripts/dynamic-ip-manager.sh detect --domain example.com 2>&1 | tee debug.log

# 分析日志
grep -E "(ERROR|WARNING)" debug.log
```

### 3. 回滚方案

```bash
# 如果新方案有问题，回滚到原版本
cp scripts/dynamic-ip-manager.sh.backup scripts/dynamic-ip-manager.sh

# 重启相关服务
kubectl rollout restart deployment/dynamic-ip-manager -n matrix-external

# 恢复原有检测间隔
export CHECK_INTERVAL="60"
```

## 📈 性能优化

### 1. 检测频率调优

```bash
# 高频检测（适用于不稳定网络）
CHECK_INTERVAL=5

# 标准检测（推荐）
CHECK_INTERVAL=10

# 低频检测（适用于稳定网络）
CHECK_INTERVAL=30
```

### 2. 超时参数调优

```bash
# 快速网络环境
ping_timeout=1
dns_timeout=3
http_timeout=5

# 标准网络环境（默认）
ping_timeout=3
dns_timeout=5
http_timeout=10

# 慢速网络环境
ping_timeout=5
dns_timeout=10
http_timeout=15
```

### 3. 资源限制

```yaml
# Kubernetes资源限制
resources:
  requests:
    memory: "64Mi"
    cpu: "50m"
  limits:
    memory: "128Mi"
    cpu: "100m"
```

## 🔐 安全考虑

### 1. API Token安全

```bash
# 使用Kubernetes Secret存储
kubectl create secret generic cloudflare-credentials \
  --from-literal=api-token="your-token" \
  -n matrix-external

# 限制API Token权限
# 只授予Zone:Read和DNS:Edit权限
```

### 2. 网络安全

```bash
# 限制出站网络访问
# 只允许访问必需的DNS服务器和API端点

# 使用NetworkPolicy限制Pod网络访问
```

### 3. 日志安全

```bash
# 避免在日志中记录敏感信息
# 使用脱敏处理敏感数据
```

## 📋 维护清单

### 每日检查
- [ ] 检查IP检测服务运行状态
- [ ] 查看错误日志
- [ ] 验证DNS记录正确性

### 每周检查  
- [ ] 运行完整功能测试
- [ ] 检查性能指标
- [ ] 更新备份配置

### 每月检查
- [ ] 审查API Token权限
- [ ] 更新依赖工具版本
- [ ] 优化配置参数

---

**文档版本**: 1.0  
**最后更新**: 2025-06-19  
**支持联系**: DevOps团队
