# 网络环境配置文件
# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

apiVersion: v1
kind: ConfigMap
metadata:
  name: network-environment-config
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: network-config
data:
  # 旁路网关配置
  bypass-gateway-mode: "auto"  # auto, enabled, disabled
  
  # 网络接口配置
  preferred-interface: ""       # 首选网络接口，如: eth0, wlan0
  interface-priority: |         # 网络接口优先级列表
    eth0
    ens3
    enp0s3
    wlan0
    wlp2s0
  
  # IP检测方法配置
  detection-methods: "stun,dns_txt,interface,dns_query,traceroute,fallback"
  method-timeout: "10"          # 每种方法的超时时间（秒）
  method-retry: "2"             # 每种方法的重试次数
  
  # STUN服务器配置
  stun-servers: |
    stun.l.google.com:19302
    stun1.l.google.com:19302
    stun.cloudflare.com:3478
    stun.nextcloud.com:443
    stun.sipgate.net:3478
  
  # DNS TXT服务配置
  dns-txt-services: |
    o-o.myaddr.l.google.com @******* TXT
    whoami.cloudflare @******* TXT
    myip.opendns.com @************** A
    resolver1.opendns.com @************** A
  
  # 备用HTTP服务配置
  fallback-http-services: |
    https://ipv4.icanhazip.com
    https://api.ipify.org
    https://checkip.amazonaws.com
    https://ipinfo.io/ip
    https://ifconfig.me/ip
    https://ident.me
  
  # 网络诊断配置
  enable-network-diagnostics: "true"
  enable-bypass-detection: "true"
  enable-proxy-detection: "true"
  enable-traceroute-analysis: "true"
  
  # 验证配置
  enable-ip-ownership-verification: "true"
  enable-ping-verification: "true"
  enable-service-verification: "true"
  
  # 性能配置
  parallel-detection: "false"   # 是否并行执行检测方法
  detection-cache-ttl: "300"    # 检测结果缓存时间（秒）
  
  # 日志配置
  log-level: "info"             # debug, info, warning, error
  enable-detailed-logging: "true"
  log-network-details: "true"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: network-troubleshooting-guide
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: troubleshooting
data:
  common-issues.md: |
    # 网络环境常见问题排查指南
    
    ## 1. 旁路网关环境
    
    ### 症状
    - 不同IP检测服务返回不同结果
    - traceroute显示路径异常
    - HTTP请求被代理或重定向
    
    ### 解决方案
    ```bash
    # 启用旁路网关模式
    ./dynamic-ip-manager.sh detect --domain example.com --bypass-gateway-mode enabled
    
    # 指定特定网络接口
    ./dynamic-ip-manager.sh detect --domain example.com --preferred-interface eth0
    
    # 使用STUN协议绕过HTTP代理
    ./dynamic-ip-manager.sh detect --domain example.com --detection-methods stun,dns_txt
    ```
    
    ## 2. 企业网络环境
    
    ### 症状
    - 外部HTTP服务无法访问
    - DNS查询被拦截或重定向
    - 防火墙阻止某些协议
    
    ### 解决方案
    ```bash
    # 仅使用DNS方法
    ./dynamic-ip-manager.sh detect --domain example.com --detection-methods dns_query,dns_txt
    
    # 启用网络诊断
    ./dynamic-ip-manager.sh diagnose --domain example.com --enable-diagnostics
    ```
    
    ## 3. 云环境NAT
    
    ### 症状
    - 检测到的IP是NAT网关IP
    - 服务验证失败
    - ping验证失败
    
    ### 解决方案
    ```bash
    # 使用traceroute分析
    ./dynamic-ip-manager.sh detect --domain example.com --detection-methods traceroute,dns_query
    
    # 禁用ping验证
    export ENABLE_PING_VERIFICATION=false
    ```
    
    ## 4. 移动网络环境
    
    ### 症状
    - IP频繁变化
    - 网络连接不稳定
    - 某些服务间歇性不可用
    
    ### 解决方案
    ```bash
    # 增加检测频率
    ./dynamic-ip-manager.sh monitor --domain example.com --check-interval 30
    
    # 使用多种检测方法
    ./dynamic-ip-manager.sh detect --domain example.com --detection-methods stun,dns_txt,fallback
    ```
  
  network-commands.md: |
    # 网络诊断命令参考
    
    ## 基本网络信息
    ```bash
    # 查看网络接口
    ip addr show
    ip link show
    
    # 查看路由表
    ip route show
    ip route get *******
    
    # 查看DNS配置
    cat /etc/resolv.conf
    systemd-resolve --status
    ```
    
    ## 连通性测试
    ```bash
    # ping测试
    ping -c 4 *******
    ping -c 4 *******
    
    # DNS解析测试
    nslookup google.com
    dig google.com
    dig @******* google.com
    
    # 端口连通性测试
    telnet ******* 53
    nc -zv ******* 53
    ```
    
    ## 路径分析
    ```bash
    # traceroute分析
    traceroute *******
    traceroute -n *******
    mtr *******
    
    # 路径MTU发现
    ping -M do -s 1472 *******
    ```
    
    ## 代理检测
    ```bash
    # 检查环境变量
    env | grep -i proxy
    
    # 检查HTTP代理
    curl -I http://httpbin.org/headers
    curl -I https://httpbin.org/headers
    
    # 检查透明代理
    curl -H "Host: example.com" http://*******/
    ```
    
    ## STUN测试
    ```bash
    # 安装STUN客户端
    apt-get install stun-client
    yum install stun
    
    # 测试STUN服务器
    stunclient stun.l.google.com
    stunclient stun.cloudflare.com 3478
    ```

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: network-optimization-profiles
  namespace: matrix-external
  labels:
    app.kubernetes.io/name: dynamic-ip-manager
    app.kubernetes.io/component: optimization
data:
  # 高速网络环境配置
  high-speed-network.env: |
    BYPASS_GATEWAY_MODE=disabled
    DETECTION_METHODS=dns_query,stun,fallback
    NETWORK_TIMEOUT=5
    CHECK_INTERVAL=10
    ENABLE_PING_VERIFICATION=true
    PARALLEL_DETECTION=true
  
  # 企业网络环境配置
  enterprise-network.env: |
    BYPASS_GATEWAY_MODE=auto
    DETECTION_METHODS=dns_query,dns_txt,interface
    NETWORK_TIMEOUT=15
    CHECK_INTERVAL=30
    ENABLE_PING_VERIFICATION=false
    PARALLEL_DETECTION=false
  
  # 移动网络环境配置
  mobile-network.env: |
    BYPASS_GATEWAY_MODE=enabled
    DETECTION_METHODS=stun,dns_txt,fallback
    NETWORK_TIMEOUT=20
    CHECK_INTERVAL=60
    ENABLE_PING_VERIFICATION=false
    PARALLEL_DETECTION=false
  
  # 受限网络环境配置
  restricted-network.env: |
    BYPASS_GATEWAY_MODE=enabled
    DETECTION_METHODS=dns_query,traceroute
    NETWORK_TIMEOUT=30
    CHECK_INTERVAL=120
    ENABLE_PING_VERIFICATION=false
    PARALLEL_DETECTION=false
  
  # 云环境NAT配置
  cloud-nat.env: |
    BYPASS_GATEWAY_MODE=auto
    DETECTION_METHODS=traceroute,dns_query,stun
    NETWORK_TIMEOUT=10
    CHECK_INTERVAL=15
    ENABLE_PING_VERIFICATION=false
    PARALLEL_DETECTION=true
