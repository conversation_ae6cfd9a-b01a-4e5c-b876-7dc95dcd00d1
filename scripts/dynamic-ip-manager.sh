#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 动态 IP 管理脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN=""
INTERNAL_PORT="8443"
EXTERNAL_NAMESPACE="matrix-external"
INTERNAL_NAMESPACE="matrix-internal"
EXTERNAL_RELEASE="matrix-external"
INTERNAL_RELEASE="matrix-internal"
CONFIG_MAP_NAME="dynamic-ip-config"
CHECK_INTERVAL="${CHECK_INTERVAL:-10}"

# 网络环境配置
BYPASS_GATEWAY_MODE="${BYPASS_GATEWAY_MODE:-auto}"  # auto, enabled, disabled
PREFERRED_INTERFACE="${PREFERRED_INTERFACE:-}"      # 首选网络接口
DETECTION_METHODS="${DETECTION_METHODS:-stun,dns_txt,interface,dns_query,traceroute,fallback}"
NETWORK_TIMEOUT="${NETWORK_TIMEOUT:-10}"
STUN_SERVERS="${STUN_SERVERS:-stun.l.google.com:19302,stun1.l.google.com:19302,stun.cloudflare.com:3478}"
ENABLE_NETWORK_DIAGNOSTICS="${ENABLE_NETWORK_DIAGNOSTICS:-true}"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
动态 IP 管理脚本

用法: $0 [选项] <操作>

操作:
  detect          检测当前公网 IP
  update          更新 well-known 配置
  monitor         持续监控 IP 变化
  setup           初始化动态 IP 管理

必需参数:
  --domain DOMAIN              主域名 (如: example.com)

可选参数:
  -h, --help                  显示此帮助信息
  --internal-port PORT        内部服务器端口 (默认: 8443)
  --external-namespace NS     外部服务器命名空间 (默认: matrix-external)
  --internal-namespace NS     内部服务器命名空间 (默认: matrix-internal)
  --check-interval SECONDS    检查间隔秒数 (默认: 10)
  --bypass-gateway-mode MODE   旁路网关模式 (auto/enabled/disabled, 默认: auto)
  --preferred-interface IFACE  首选网络接口 (如: eth0, wlan0)
  --detection-methods METHODS  检测方法列表 (逗号分隔, 默认: stun,dns_txt,interface,dns_query,traceroute,fallback)
  --network-timeout SECONDS   网络超时设置 (默认: 10)
  --enable-diagnostics         启用网络环境诊断

示例:
  $0 detect --domain example.com
  $0 update --domain example.com
  $0 monitor --domain example.com --check-interval 300
  $0 diagnose --domain example.com --enable-diagnostics
  $0 detect --domain example.com --bypass-gateway-mode enabled --preferred-interface eth0

EOF
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --internal-port)
            INTERNAL_PORT="$2"
            shift 2
            ;;
        --external-namespace)
            EXTERNAL_NAMESPACE="$2"
            shift 2
            ;;
        --internal-namespace)
            INTERNAL_NAMESPACE="$2"
            shift 2
            ;;
        --check-interval)
            CHECK_INTERVAL="$2"
            shift 2
            ;;
        --bypass-gateway-mode)
            BYPASS_GATEWAY_MODE="$2"
            shift 2
            ;;
        --preferred-interface)
            PREFERRED_INTERFACE="$2"
            shift 2
            ;;
        --detection-methods)
            DETECTION_METHODS="$2"
            shift 2
            ;;
        --network-timeout)
            NETWORK_TIMEOUT="$2"
            shift 2
            ;;
        --enable-diagnostics)
            ENABLE_NETWORK_DIAGNOSTICS="true"
            shift
            ;;
        detect|update|monitor|setup|diagnose)
            ACTION="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$DOMAIN" ]]; then
    log_error "缺少必需参数: --domain"
    show_help
    exit 1
fi

if [[ -z "${ACTION:-}" ]]; then
    log_error "缺少操作参数"
    show_help
    exit 1
fi

# 网络环境诊断
diagnose_network_environment() {
    log_info "=== 网络环境诊断 ==="

    # 检查网络接口
    log_info "检查网络接口..."
    ip addr show | grep -E "^[0-9]+:" | while read -r line; do
        local interface=$(echo "$line" | awk '{print $2}' | sed 's/://')
        local status=$(echo "$line" | grep -o "state [A-Z]*" | awk '{print $2}')
        log_info "  接口: $interface, 状态: $status"
    done

    # 检查默认路由
    log_info "检查默认路由..."
    local default_route=$(ip route get ******* 2>/dev/null)
    if [[ -n "$default_route" ]]; then
        log_info "  默认路由: $default_route"
        local default_interface=$(echo "$default_route" | grep -o "dev [^ ]*" | awk '{print $2}')
        local default_gateway=$(echo "$default_route" | grep -o "via [^ ]*" | awk '{print $2}')
        log_info "  默认接口: $default_interface"
        log_info "  默认网关: $default_gateway"
    else
        log_warning "  无法获取默认路由信息"
    fi

    # 检查DNS配置
    log_info "检查DNS配置..."
    if [[ -f /etc/resolv.conf ]]; then
        grep "nameserver" /etc/resolv.conf | while read -r line; do
            log_info "  $line"
        done
    fi

    # 检查代理设置
    log_info "检查代理设置..."
    for proxy_var in http_proxy https_proxy HTTP_PROXY HTTPS_PROXY; do
        if [[ -n "${!proxy_var:-}" ]]; then
            log_warning "  检测到代理设置: $proxy_var=${!proxy_var}"
        fi
    done

    # 检测旁路网关
    log_info "检测旁路网关..."
    if detect_bypass_gateway; then
        log_warning "  检测到可能的旁路网关环境"
        BYPASS_GATEWAY_MODE="enabled"
    else
        log_info "  未检测到旁路网关"
        if [[ "$BYPASS_GATEWAY_MODE" == "auto" ]]; then
            BYPASS_GATEWAY_MODE="disabled"
        fi
    fi

    log_info "网络环境诊断完成，旁路网关模式: $BYPASS_GATEWAY_MODE"
}

# 检测旁路网关
detect_bypass_gateway() {
    # 方法1：检查traceroute路径异常
    local traceroute_output
    if command -v traceroute >/dev/null; then
        traceroute_output=$(timeout 10 traceroute -m 5 ******* 2>/dev/null || echo "")
        if [[ "$traceroute_output" == *"* * *"* ]]; then
            log_info "traceroute检测到路径异常，可能存在旁路网关"
            return 0
        fi
    fi

    # 方法2：检查多个外部服务的IP检测结果差异
    local ip1 ip2 ip3
    ip1=$(timeout 5 curl -s --max-time 3 https://ipv4.icanhazip.com 2>/dev/null || echo "")
    ip2=$(timeout 5 curl -s --max-time 3 https://api.ipify.org 2>/dev/null || echo "")
    ip3=$(timeout 5 curl -s --max-time 3 https://checkip.amazonaws.com 2>/dev/null || echo "")

    if [[ -n "$ip1" && -n "$ip2" && -n "$ip3" ]]; then
        if [[ "$ip1" != "$ip2" || "$ip2" != "$ip3" || "$ip1" != "$ip3" ]]; then
            log_info "多个IP检测服务返回不同结果，可能存在旁路网关"
            log_info "  icanhazip: $ip1"
            log_info "  ipify: $ip2"
            log_info "  amazonaws: $ip3"
            return 0
        fi
    fi

    # 方法3：检查HTTP头中的代理信息
    local headers
    headers=$(timeout 5 curl -s -I https://httpbin.org/headers 2>/dev/null || echo "")
    if [[ "$headers" == *"X-Forwarded-For"* || "$headers" == *"X-Real-IP"* ]]; then
        log_info "检测到HTTP代理头，可能存在旁路网关"
        return 0
    fi

    return 1
}

# 智能检测公网IP - 适应复杂网络环境
detect_public_ip() {
    log_info "开始智能检测公网 IP..."

    # 如果启用诊断，先进行网络环境诊断
    if [[ "$ENABLE_NETWORK_DIAGNOSTICS" == "true" ]]; then
        diagnose_network_environment
    fi

    # 解析检测方法列表
    IFS=',' read -ra methods <<< "$DETECTION_METHODS"

    local detected_ip=""
    local method_count=0

    # 按优先级尝试各种检测方法
    for method in "${methods[@]}"; do
        method=$(echo "$method" | xargs)  # 去除空格
        ((method_count++))

        log_info "尝试检测方法 $method_count: $method"

        case "$method" in
            "stun")
                detected_ip=$(detect_ip_via_stun)
                ;;
            "dns_txt")
                detected_ip=$(detect_ip_via_dns_txt)
                ;;
            "interface")
                detected_ip=$(detect_ip_via_interface)
                ;;
            "dns_query")
                detected_ip=$(detect_ip_via_dns_query)
                ;;
            "traceroute")
                detected_ip=$(detect_ip_via_traceroute)
                ;;
            "fallback")
                detected_ip=$(detect_ip_fallback)
                ;;
            *)
                log_warning "未知的检测方法: $method"
                continue
                ;;
        esac

        # 如果检测成功，验证IP并返回
        if [[ -n "$detected_ip" && "$detected_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            log_success "方法 $method 检测成功，IP: $detected_ip"

            # 验证IP可达性和所有权
            if verify_ip_comprehensive "$detected_ip"; then
                log_success "IP验证通过，最终结果: $detected_ip"
                echo "$detected_ip"
                return 0
            else
                log_warning "IP验证失败，尝试下一种方法"
                detected_ip=""
            fi
        else
            log_warning "方法 $method 检测失败"
        fi

        # 方法间延迟
        sleep 1
    done

    # 所有方法都失败
    log_error "所有 IP 检测方法都失败，无法检测到有效的公网 IP"
    log_error "网络环境诊断建议："
    log_error "1. 检查网络连接是否正常"
    log_error "2. 确认DNS记录 ip.${DOMAIN} 已正确配置"
    log_error "3. 检查防火墙和代理设置"
    log_error "4. 尝试手动指定网络接口: --preferred-interface"
    log_error "5. 启用旁路网关模式: --bypass-gateway-mode enabled"
    return 1
}

# STUN协议检测IP
detect_ip_via_stun() {
    log_info "使用STUN协议检测IP..."

    # 检查是否安装了stun客户端工具
    if ! command -v stunclient >/dev/null 2>&1; then
        log_warning "stunclient未安装，跳过STUN检测"
        return 1
    fi

    # 解析STUN服务器列表
    IFS=',' read -ra stun_servers <<< "$STUN_SERVERS"

    for stun_server in "${stun_servers[@]}"; do
        stun_server=$(echo "$stun_server" | xargs)  # 去除空格
        log_info "尝试STUN服务器: $stun_server"

        local stun_result
        stun_result=$(timeout "$NETWORK_TIMEOUT" stunclient "$stun_server" 2>/dev/null || echo "")

        if [[ -n "$stun_result" ]]; then
            local detected_ip
            detected_ip=$(echo "$stun_result" | grep -oE '[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | head -1)

            if [[ -n "$detected_ip" ]]; then
                log_success "STUN检测成功: $detected_ip (服务器: $stun_server)"
                echo "$detected_ip"
                return 0
            fi
        fi

        sleep 1
    done

    log_warning "所有STUN服务器检测失败"
    return 1
}

# DNS TXT记录检测IP
detect_ip_via_dns_txt() {
    log_info "使用DNS TXT记录检测IP..."

    local dns_services=(
        "o-o.myaddr.l.google.com @******* TXT"
        "whoami.cloudflare @******* TXT"
        "myip.opendns.com @************** A"
    )

    for service in "${dns_services[@]}"; do
        log_info "尝试DNS服务: $service"

        local detected_ip
        detected_ip=$(timeout "$NETWORK_TIMEOUT" dig +short $service 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | head -1)

        if [[ -n "$detected_ip" ]]; then
            log_success "DNS TXT检测成功: $detected_ip"
            echo "$detected_ip"
            return 0
        fi

        sleep 1
    done

    log_warning "DNS TXT记录检测失败"
    return 1
}

# 指定网络接口检测IP
detect_ip_via_interface() {
    log_info "使用指定网络接口检测IP..."

    local interfaces=()

    # 如果指定了首选接口，优先使用
    if [[ -n "$PREFERRED_INTERFACE" ]]; then
        interfaces+=("$PREFERRED_INTERFACE")
    fi

    # 自动检测活跃的网络接口
    while IFS= read -r interface; do
        if [[ "$interface" != "lo" && "$interface" != "$PREFERRED_INTERFACE" ]]; then
            interfaces+=("$interface")
        fi
    done < <(ip route | grep default | awk '{print $5}' | sort -u)

    local fallback_services=(
        "https://ipv4.icanhazip.com"
        "https://api.ipify.org"
        "https://checkip.amazonaws.com"
    )

    for interface in "${interfaces[@]}"; do
        log_info "尝试网络接口: $interface"

        # 检查接口是否存在且活跃
        if ! ip link show "$interface" >/dev/null 2>&1; then
            log_warning "接口 $interface 不存在，跳过"
            continue
        fi

        local interface_status
        interface_status=$(ip link show "$interface" | grep -o "state [A-Z]*" | awk '{print $2}')
        if [[ "$interface_status" != "UP" ]]; then
            log_warning "接口 $interface 状态为 $interface_status，跳过"
            continue
        fi

        # 使用指定接口进行HTTP请求
        for service in "${fallback_services[@]}"; do
            log_info "通过接口 $interface 访问 $service"

            local detected_ip
            if [[ "$BYPASS_GATEWAY_MODE" == "enabled" ]]; then
                # 旁路网关模式：使用--interface参数
                detected_ip=$(timeout "$NETWORK_TIMEOUT" curl -s --interface "$interface" --max-time 5 "$service" 2>/dev/null | grep -oE '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' | head -1)
            else
                # 标准模式
                detected_ip=$(timeout "$NETWORK_TIMEOUT" curl -s --max-time 5 "$service" 2>/dev/null | grep -oE '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' | head -1)
            fi

            if [[ -n "$detected_ip" ]]; then
                log_success "接口检测成功: $detected_ip (接口: $interface, 服务: $service)"
                echo "$detected_ip"
                return 0
            fi

            sleep 1
        done
    done

    log_warning "网络接口检测失败"
    return 1
}

# DNS查询检测IP (原有方法的改进版)
detect_ip_via_dns_query() {
    log_info "使用DNS查询检测IP..."

    local dns_servers=("*******" "*******" "************")
    local dns_names=("Cloudflare" "Google" "腾讯DNSPod")
    local ping_target="ip.${DOMAIN}"

    for i in "${!dns_servers[@]}"; do
        local dns_server="${dns_servers[$i]}"
        local dns_name="${dns_names[$i]}"

        log_info "尝试 ${dns_name} DNS (${dns_server})"

        # 测试DNS服务器可达性
        if ! timeout 3 ping -c 1 "$dns_server" >/dev/null 2>&1; then
            log_warning "${dns_name} DNS 服务器不可达，跳过"
            continue
        fi

        # DNS解析
        local detected_ip
        detected_ip=$(timeout "$NETWORK_TIMEOUT" dig +short +time=5 +tries=2 "$ping_target" @"$dns_server" 2>/dev/null | head -n1 | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' || echo "")

        if [[ -n "$detected_ip" ]]; then
            log_success "DNS查询检测成功: $detected_ip (DNS: ${dns_name})"
            echo "$detected_ip"
            return 0
        fi

        sleep 1
    done

    log_warning "DNS查询检测失败"
    return 1
}

# traceroute分析检测IP
detect_ip_via_traceroute() {
    log_info "使用traceroute分析检测IP..."

    if ! command -v traceroute >/dev/null 2>&1; then
        log_warning "traceroute未安装，跳过此方法"
        return 1
    fi

    local target_hosts=("*******" "*******" "**************")

    for target in "${target_hosts[@]}"; do
        log_info "traceroute到 $target"

        local traceroute_output
        traceroute_output=$(timeout "$NETWORK_TIMEOUT" traceroute -m 10 -w 2 "$target" 2>/dev/null || echo "")

        if [[ -n "$traceroute_output" ]]; then
            # 分析traceroute输出，查找最后一个公网IP
            local last_public_ip
            last_public_ip=$(echo "$traceroute_output" | grep -oE '[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+' | grep -v '^10\.' | grep -v '^192\.168\.' | grep -v '^172\.(1[6-9]|2[0-9]|3[01])\.' | tail -1)

            if [[ -n "$last_public_ip" && "$last_public_ip" != "$target" ]]; then
                log_success "traceroute检测成功: $last_public_ip"
                echo "$last_public_ip"
                return 0
            fi
        fi

        sleep 2
    done

    log_warning "traceroute分析检测失败"
    return 1
}

# 综合验证IP
verify_ip_comprehensive() {
    local ip_to_verify="$1"

    log_info "综合验证IP: $ip_to_verify"

    # 1. 基本格式验证
    if ! [[ "$ip_to_verify" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_error "IP格式无效: $ip_to_verify"
        return 1
    fi

    # 2. 检查是否为私有IP
    if [[ "$ip_to_verify" =~ ^10\. ]] || \
       [[ "$ip_to_verify" =~ ^192\.168\. ]] || \
       [[ "$ip_to_verify" =~ ^172\.(1[6-9]|2[0-9]|3[01])\. ]]; then
        log_error "检测到私有IP地址: $ip_to_verify"
        return 1
    fi

    # 3. ping可达性验证
    if ! timeout 5 ping -c 2 "$ip_to_verify" >/dev/null 2>&1; then
        log_warning "IP ping验证失败: $ip_to_verify"
        # 不直接返回失败，因为某些网络环境可能禁ping
    fi

    # 4. 服务所有权验证
    if [[ -n "${DOMAIN}" ]]; then
        if timeout 10 curl -s -f --max-time 5 --connect-to "${DOMAIN}:443:${ip_to_verify}:443" \
           "https://${DOMAIN}/.well-known/matrix/server" >/dev/null 2>&1; then
            log_success "IP所有权验证通过: $ip_to_verify"
            return 0
        else
            log_warning "IP所有权验证失败，但继续使用: $ip_to_verify"
            # 在某些网络环境下，这个验证可能失败，但IP仍然有效
        fi
    fi

    log_info "IP验证完成: $ip_to_verify"
    return 0
}

# 备用IP检测方法
detect_ip_fallback() {
    local fallback_services=(
        "https://ipv4.icanhazip.com"
        "https://api.ipify.org"
        "https://checkip.amazonaws.com"
    )

    log_info "使用备用IP检测服务..."

    for service in "${fallback_services[@]}"; do
        log_info "尝试服务: $service"
        local detected_ip
        detected_ip=$(curl -s --max-time 10 "$service" 2>/dev/null | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$' | head -n1)

        if [[ -n "$detected_ip" ]]; then
            log_info "备用服务检测到 IP: $detected_ip"
            # 验证IP格式
            if [[ "$detected_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                echo "$detected_ip"
                return 0
            fi
        fi

        sleep 2
    done

    log_error "所有备用IP检测服务都失败"
    return 1
}

# 获取当前存储的 IP
get_stored_ip() {
    kubectl get configmap "$CONFIG_MAP_NAME" -n "$EXTERNAL_NAMESPACE" -o jsonpath='{.data.current-ip}' 2>/dev/null || echo ""
}

# 存储当前 IP
store_ip() {
    local ip="$1"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 创建或更新 ConfigMap
    kubectl create configmap "$CONFIG_MAP_NAME" \
        --from-literal=current-ip="$ip" \
        --from-literal=last-update="$timestamp" \
        --from-literal=domain="$DOMAIN" \
        --from-literal=internal-port="$INTERNAL_PORT" \
        -n "$EXTERNAL_NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "IP 已存储: $ip (更新时间: $timestamp)"
}

# 更新 DNS 记录 - 使用 Cloudflare API
update_dns_records() {
    local new_ip="$1"

    log_info "更新 DNS 记录到新 IP: $new_ip"

    # 需要更新的子域名列表
    local subdomains=("matrix" "mas" "element" "rtc")

    # 检查是否有 Cloudflare API Token
    if [[ -z "${CLOUDFLARE_API_TOKEN:-}" ]]; then
        log_error "未设置 CLOUDFLARE_API_TOKEN 环境变量"
        log_error "请设置 Cloudflare API Token 以自动更新 DNS 记录"
        return 1
    fi

    # 获取 Zone ID（如果未设置）
    if [[ -z "${CLOUDFLARE_ZONE_ID:-}" ]]; then
        log_info "获取域名 $DOMAIN 的 Zone ID..."
        CLOUDFLARE_ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$DOMAIN" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json" | \
            jq -r '.result[0].id // empty')

        if [[ -z "$CLOUDFLARE_ZONE_ID" ]]; then
            log_error "无法获取域名 $DOMAIN 的 Zone ID"
            return 1
        fi
        log_success "获取到 Zone ID: $CLOUDFLARE_ZONE_ID"
    fi

    # 更新每个子域名的 DNS 记录
    for subdomain in "${subdomains[@]}"; do
        local full_domain="${subdomain}.${DOMAIN}"
        log_info "更新 DNS 记录: $full_domain -> $new_ip"

        # 获取现有记录 ID
        local record_id
        record_id=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records?name=$full_domain&type=A" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json" | \
            jq -r '.result[0].id // empty')

        if [[ -n "$record_id" ]]; then
            # 更新现有记录
            local response
            response=$(curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records/$record_id" \
                -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
                -H "Content-Type: application/json" \
                --data "{
                    \"type\": \"A\",
                    \"name\": \"$full_domain\",
                    \"content\": \"$new_ip\",
                    \"ttl\": 60
                }")

            if echo "$response" | jq -e '.success' >/dev/null; then
                log_success "DNS 记录更新成功: $full_domain"
            else
                log_error "DNS 记录更新失败: $full_domain"
                echo "$response" | jq '.errors' >&2
            fi
        else
            log_warning "未找到 DNS 记录: $full_domain，跳过更新"
        fi
    done
}

# 验证 DNS 记录更新
verify_dns_update() {
    local expected_ip="$1"

    log_info "验证 DNS 记录更新..."

    local subdomains=("matrix" "mas" "element" "rtc")
    local all_updated=true

    for subdomain in "${subdomains[@]}"; do
        local full_domain="${subdomain}.${DOMAIN}"
        local resolved_ip

        # 等待 DNS 传播
        sleep 10

        # 检查 DNS 解析结果
        resolved_ip=$(dig +short "$full_domain" @******* 2>/dev/null | head -n1 || echo "")

        if [[ "$resolved_ip" == "$expected_ip" ]]; then
            log_success "DNS 验证通过: $full_domain -> $resolved_ip"
        else
            log_error "DNS 验证失败: $full_domain -> $resolved_ip (期望: $expected_ip)"
            all_updated=false
        fi
    done

    if [[ "$all_updated" == "true" ]]; then
        log_success "所有 DNS 记录验证通过"
        return 0
    else
        log_error "部分 DNS 记录验证失败"
        return 1
    fi
}

# 验证服务可访问性
verify_service_accessibility() {
    local new_ip="$1"

    log_info "验证服务可访问性..."

    # 等待 DNS 传播完成
    sleep 30

    # 检查各个服务端点
    local services=(
        "matrix.${DOMAIN}:${INTERNAL_PORT}/_matrix/client/versions"
        "mas.${DOMAIN}:${INTERNAL_PORT}/health"
        "element.${DOMAIN}:${INTERNAL_PORT}/health"
        "rtc.${DOMAIN}:${INTERNAL_PORT}/health"
    )

    local all_accessible=true

    for service in "${services[@]}"; do
        local url="https://$service"
        log_info "检查服务: $url"

        if curl -s -f --max-time 10 "$url" >/dev/null 2>&1; then
            log_success "服务可访问: $service"
        else
            log_error "服务不可访问: $service"
            all_accessible=false
        fi
    done

    # 验证 well-known 委托（应该保持不变）
    local server_response
    server_response=$(curl -s --max-time 10 "https://${DOMAIN}/.well-known/matrix/server" 2>/dev/null || echo "")

    if [[ "$server_response" == *"matrix.${DOMAIN}:${INTERNAL_PORT}"* ]]; then
        log_success "well-known/server 委托正常"
    else
        log_error "well-known/server 委托异常"
        log_error "响应内容: $server_response"
        all_accessible=false
    fi

    if [[ "$all_accessible" == "true" ]]; then
        log_success "所有服务验证通过"
        return 0
    else
        log_error "部分服务验证失败"
        return 1
    fi
}

# 发送通知
send_notification() {
    local message="$1"
    local old_ip="$2"
    local new_ip="$3"
    
    # 这里可以集成各种通知方式
    log_info "发送通知: $message"
    
    # 示例：发送到 Slack/Discord/邮件等
    # curl -X POST "$WEBHOOK_URL" -d "{\"text\": \"$message\"}"
    
    # 记录到系统日志
    logger "Matrix Dynamic IP Update: $message (Old: $old_ip, New: $new_ip)"
}

# 初始化动态 IP 管理
setup_dynamic_ip() {
    log_info "初始化动态 IP 管理..."
    
    # 检测当前 IP
    local current_ip
    if current_ip=$(detect_public_ip); then
        log_success "检测到当前 IP: $current_ip"
        
        # 存储 IP
        store_ip "$current_ip"

        # 更新 DNS 记录
        if update_dns_records "$current_ip"; then
            verify_dns_update "$current_ip"
            verify_service_accessibility "$current_ip"
            log_success "动态 IP 管理初始化完成"
        else
            log_error "DNS 记录更新失败"
            return 1
        fi
    else
        log_error "IP 检测失败"
        return 1
    fi
}

# 执行 IP 更新
perform_ip_update() {
    local current_ip stored_ip
    
    # 检测当前 IP
    if ! current_ip=$(detect_public_ip); then
        log_error "IP 检测失败"
        return 1
    fi
    
    # 获取存储的 IP
    stored_ip=$(get_stored_ip)
    
    log_info "当前 IP: $current_ip"
    log_info "存储的 IP: $stored_ip"
    
    # 比较 IP 是否发生变化
    if [[ "$current_ip" != "$stored_ip" ]]; then
        log_warning "检测到 IP 变化: $stored_ip -> $current_ip"
        
        # 更新存储的 IP
        store_ip "$current_ip"

        # 更新 DNS 记录
        if update_dns_records "$current_ip"; then
            # 验证 DNS 更新
            if verify_dns_update "$current_ip"; then
                # 验证服务可访问性
                if verify_service_accessibility "$current_ip"; then
                    send_notification "IP 地址已更新并验证成功" "$stored_ip" "$current_ip"
                    log_success "IP 更新完成: $current_ip"
                else
                    send_notification "IP 地址更新后服务验证失败" "$stored_ip" "$current_ip"
                    log_error "服务可访问性验证失败"
                    return 1
                fi
            else
                send_notification "IP 地址更新后 DNS 验证失败" "$stored_ip" "$current_ip"
                log_error "DNS 验证失败"
                return 1
            fi
        else
            log_error "DNS 记录更新失败"
            return 1
        fi
    else
        log_info "IP 未发生变化，无需更新"
    fi
}

# 持续监控 IP 变化
monitor_ip_changes() {
    log_info "开始监控 IP 变化，检查间隔: ${CHECK_INTERVAL}秒"
    
    while true; do
        log_info "执行 IP 检查..."
        
        if perform_ip_update; then
            log_info "IP 检查完成"
        else
            log_error "IP 检查失败"
        fi
        
        log_info "等待 ${CHECK_INTERVAL}秒后进行下次检查..."
        sleep "$CHECK_INTERVAL"
    done
}

# 主函数
main() {
    case "$ACTION" in
        detect)
            if current_ip=$(detect_public_ip); then
                echo "$current_ip"
                log_success "当前公网 IP: $current_ip"
            else
                exit 1
            fi
            ;;
        update)
            perform_ip_update
            ;;
        monitor)
            monitor_ip_changes
            ;;
        setup)
            setup_dynamic_ip
            ;;
        diagnose)
            log_info "开始网络环境诊断..."
            diagnose_network_environment

            log_info "测试各种IP检测方法..."
            IFS=',' read -ra methods <<< "$DETECTION_METHODS"
            for method in "${methods[@]}"; do
                method=$(echo "$method" | xargs)
                log_info "测试方法: $method"

                case "$method" in
                    "stun")
                        detect_ip_via_stun || log_warning "STUN方法不可用"
                        ;;
                    "dns_txt")
                        detect_ip_via_dns_txt || log_warning "DNS TXT方法不可用"
                        ;;
                    "interface")
                        detect_ip_via_interface || log_warning "网络接口方法不可用"
                        ;;
                    "dns_query")
                        detect_ip_via_dns_query || log_warning "DNS查询方法不可用"
                        ;;
                    "traceroute")
                        detect_ip_via_traceroute || log_warning "traceroute方法不可用"
                        ;;
                    "fallback")
                        detect_ip_fallback || log_warning "备用方法不可用"
                        ;;
                esac
                echo "---"
            done

            log_success "网络环境诊断完成"
            ;;
        *)
            log_error "未知操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main
