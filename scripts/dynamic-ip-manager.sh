#!/bin/bash

# Copyright 2025 New Vector Ltd
# SPDX-License-Identifier: AGPL-3.0-only

# 动态 IP 管理脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN=""
INTERNAL_PORT="8443"
EXTERNAL_NAMESPACE="matrix-external"
INTERNAL_NAMESPACE="matrix-internal"
EXTERNAL_RELEASE="matrix-external"
INTERNAL_RELEASE="matrix-internal"
CONFIG_MAP_NAME="dynamic-ip-config"
CHECK_INTERVAL="${CHECK_INTERVAL:-60}"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << EOF
动态 IP 管理脚本

用法: $0 [选项] <操作>

操作:
  detect          检测当前公网 IP
  update          更新 well-known 配置
  monitor         持续监控 IP 变化
  setup           初始化动态 IP 管理

必需参数:
  --domain DOMAIN              主域名 (如: example.com)

可选参数:
  -h, --help                  显示此帮助信息
  --internal-port PORT        内部服务器端口 (默认: 8443)
  --external-namespace NS     外部服务器命名空间 (默认: matrix-external)
  --internal-namespace NS     内部服务器命名空间 (默认: matrix-internal)
  --check-interval SECONDS    检查间隔秒数 (默认: 60)

示例:
  $0 detect --domain example.com
  $0 update --domain example.com
  $0 monitor --domain example.com --check-interval 300

EOF
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --domain)
            DOMAIN="$2"
            shift 2
            ;;
        --internal-port)
            INTERNAL_PORT="$2"
            shift 2
            ;;
        --external-namespace)
            EXTERNAL_NAMESPACE="$2"
            shift 2
            ;;
        --internal-namespace)
            INTERNAL_NAMESPACE="$2"
            shift 2
            ;;
        --check-interval)
            CHECK_INTERVAL="$2"
            shift 2
            ;;
        detect|update|monitor|setup)
            ACTION="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$DOMAIN" ]]; then
    log_error "缺少必需参数: --domain"
    show_help
    exit 1
fi

if [[ -z "${ACTION:-}" ]]; then
    log_error "缺少操作参数"
    show_help
    exit 1
fi

# 检测公网 IP
detect_public_ip() {
    local detected_ip=""
    
    # 主要检测方法：使用 Cloudflare DNS (*******) - 每分钟执行
    log_info "使用主要 DNS 方法检测 IP: dig +short ip.${DOMAIN} @*******"
    detected_ip=$(dig +short ip.${DOMAIN} @******* 2>/dev/null | head -n1 || echo "")

    # 验证主要方法结果
    if [[ -n "$detected_ip" && "$detected_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_success "主要 DNS 方法成功检测到 IP: $detected_ip"
        echo "$detected_ip"
        return 0
    fi

    # 备用方法 1：使用 Google DNS (*******)
    log_warning "主要方法失败，尝试备用 DNS 方法 1: dig +short ip.${DOMAIN} @*******"
    detected_ip=$(dig +short ip.${DOMAIN} @******* 2>/dev/null | head -n1 || echo "")

    # 验证备用方法 1 结果
    if [[ -n "$detected_ip" && "$detected_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_success "备用 DNS 方法 1 成功检测到 IP: $detected_ip"
        echo "$detected_ip"
        return 0
    fi

    # 备用方法 2：使用 Quad9 DNS (*******)
    log_warning "备用方法 1 失败，尝试备用 DNS 方法 2: dig +short ip.${DOMAIN} @*******"
    detected_ip=$(dig +short ip.${DOMAIN} @******* 2>/dev/null | head -n1 || echo "")

    # 验证备用方法 2 结果
    if [[ -n "$detected_ip" && "$detected_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_success "备用 DNS 方法 2 成功检测到 IP: $detected_ip"
        echo "$detected_ip"
        return 0
    fi

    # 所有 DNS 方法都失败
    log_error "所有 DNS 检测方法都失败，无法检测到有效的公网 IP"
    log_error "请确保："
    log_error "1. DNS 记录 ip.${DOMAIN} 已正确配置并指向当前公网 IP"
    log_error "2. 网络连接正常，可以访问 DNS 服务器"
    log_error "3. DNS 服务器 (*******, *******, *******) 可访问"
    return 1
}

# 获取当前存储的 IP
get_stored_ip() {
    kubectl get configmap "$CONFIG_MAP_NAME" -n "$EXTERNAL_NAMESPACE" -o jsonpath='{.data.current-ip}' 2>/dev/null || echo ""
}

# 存储当前 IP
store_ip() {
    local ip="$1"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # 创建或更新 ConfigMap
    kubectl create configmap "$CONFIG_MAP_NAME" \
        --from-literal=current-ip="$ip" \
        --from-literal=last-update="$timestamp" \
        --from-literal=domain="$DOMAIN" \
        --from-literal=internal-port="$INTERNAL_PORT" \
        -n "$EXTERNAL_NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    log_success "IP 已存储: $ip (更新时间: $timestamp)"
}

# 更新 DNS 记录 - 使用 Cloudflare API
update_dns_records() {
    local new_ip="$1"

    log_info "更新 DNS 记录到新 IP: $new_ip"

    # 需要更新的子域名列表
    local subdomains=("matrix" "mas" "element" "rtc")

    # 检查是否有 Cloudflare API Token
    if [[ -z "${CLOUDFLARE_API_TOKEN:-}" ]]; then
        log_error "未设置 CLOUDFLARE_API_TOKEN 环境变量"
        log_error "请设置 Cloudflare API Token 以自动更新 DNS 记录"
        return 1
    fi

    # 获取 Zone ID（如果未设置）
    if [[ -z "${CLOUDFLARE_ZONE_ID:-}" ]]; then
        log_info "获取域名 $DOMAIN 的 Zone ID..."
        CLOUDFLARE_ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=$DOMAIN" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json" | \
            jq -r '.result[0].id // empty')

        if [[ -z "$CLOUDFLARE_ZONE_ID" ]]; then
            log_error "无法获取域名 $DOMAIN 的 Zone ID"
            return 1
        fi
        log_success "获取到 Zone ID: $CLOUDFLARE_ZONE_ID"
    fi

    # 更新每个子域名的 DNS 记录
    for subdomain in "${subdomains[@]}"; do
        local full_domain="${subdomain}.${DOMAIN}"
        log_info "更新 DNS 记录: $full_domain -> $new_ip"

        # 获取现有记录 ID
        local record_id
        record_id=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records?name=$full_domain&type=A" \
            -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
            -H "Content-Type: application/json" | \
            jq -r '.result[0].id // empty')

        if [[ -n "$record_id" ]]; then
            # 更新现有记录
            local response
            response=$(curl -s -X PUT "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/dns_records/$record_id" \
                -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
                -H "Content-Type: application/json" \
                --data "{
                    \"type\": \"A\",
                    \"name\": \"$full_domain\",
                    \"content\": \"$new_ip\",
                    \"ttl\": 60
                }")

            if echo "$response" | jq -e '.success' >/dev/null; then
                log_success "DNS 记录更新成功: $full_domain"
            else
                log_error "DNS 记录更新失败: $full_domain"
                echo "$response" | jq '.errors' >&2
            fi
        else
            log_warning "未找到 DNS 记录: $full_domain，跳过更新"
        fi
    done
}

# 验证 DNS 记录更新
verify_dns_update() {
    local expected_ip="$1"

    log_info "验证 DNS 记录更新..."

    local subdomains=("matrix" "mas" "element" "rtc")
    local all_updated=true

    for subdomain in "${subdomains[@]}"; do
        local full_domain="${subdomain}.${DOMAIN}"
        local resolved_ip

        # 等待 DNS 传播
        sleep 10

        # 检查 DNS 解析结果
        resolved_ip=$(dig +short "$full_domain" @******* 2>/dev/null | head -n1 || echo "")

        if [[ "$resolved_ip" == "$expected_ip" ]]; then
            log_success "DNS 验证通过: $full_domain -> $resolved_ip"
        else
            log_error "DNS 验证失败: $full_domain -> $resolved_ip (期望: $expected_ip)"
            all_updated=false
        fi
    done

    if [[ "$all_updated" == "true" ]]; then
        log_success "所有 DNS 记录验证通过"
        return 0
    else
        log_error "部分 DNS 记录验证失败"
        return 1
    fi
}

# 验证服务可访问性
verify_service_accessibility() {
    local new_ip="$1"

    log_info "验证服务可访问性..."

    # 等待 DNS 传播完成
    sleep 30

    # 检查各个服务端点
    local services=(
        "matrix.${DOMAIN}:${INTERNAL_PORT}/_matrix/client/versions"
        "mas.${DOMAIN}:${INTERNAL_PORT}/health"
        "element.${DOMAIN}:${INTERNAL_PORT}/health"
        "rtc.${DOMAIN}:${INTERNAL_PORT}/health"
    )

    local all_accessible=true

    for service in "${services[@]}"; do
        local url="https://$service"
        log_info "检查服务: $url"

        if curl -s -f --max-time 10 "$url" >/dev/null 2>&1; then
            log_success "服务可访问: $service"
        else
            log_error "服务不可访问: $service"
            all_accessible=false
        fi
    done

    # 验证 well-known 委托（应该保持不变）
    local server_response
    server_response=$(curl -s --max-time 10 "https://${DOMAIN}/.well-known/matrix/server" 2>/dev/null || echo "")

    if [[ "$server_response" == *"matrix.${DOMAIN}:${INTERNAL_PORT}"* ]]; then
        log_success "well-known/server 委托正常"
    else
        log_error "well-known/server 委托异常"
        log_error "响应内容: $server_response"
        all_accessible=false
    fi

    if [[ "$all_accessible" == "true" ]]; then
        log_success "所有服务验证通过"
        return 0
    else
        log_error "部分服务验证失败"
        return 1
    fi
}

# 发送通知
send_notification() {
    local message="$1"
    local old_ip="$2"
    local new_ip="$3"
    
    # 这里可以集成各种通知方式
    log_info "发送通知: $message"
    
    # 示例：发送到 Slack/Discord/邮件等
    # curl -X POST "$WEBHOOK_URL" -d "{\"text\": \"$message\"}"
    
    # 记录到系统日志
    logger "Matrix Dynamic IP Update: $message (Old: $old_ip, New: $new_ip)"
}

# 初始化动态 IP 管理
setup_dynamic_ip() {
    log_info "初始化动态 IP 管理..."
    
    # 检测当前 IP
    local current_ip
    if current_ip=$(detect_public_ip); then
        log_success "检测到当前 IP: $current_ip"
        
        # 存储 IP
        store_ip "$current_ip"

        # 更新 DNS 记录
        if update_dns_records "$current_ip"; then
            verify_dns_update "$current_ip"
            verify_service_accessibility "$current_ip"
            log_success "动态 IP 管理初始化完成"
        else
            log_error "DNS 记录更新失败"
            return 1
        fi
    else
        log_error "IP 检测失败"
        return 1
    fi
}

# 执行 IP 更新
perform_ip_update() {
    local current_ip stored_ip
    
    # 检测当前 IP
    if ! current_ip=$(detect_public_ip); then
        log_error "IP 检测失败"
        return 1
    fi
    
    # 获取存储的 IP
    stored_ip=$(get_stored_ip)
    
    log_info "当前 IP: $current_ip"
    log_info "存储的 IP: $stored_ip"
    
    # 比较 IP 是否发生变化
    if [[ "$current_ip" != "$stored_ip" ]]; then
        log_warning "检测到 IP 变化: $stored_ip -> $current_ip"
        
        # 更新存储的 IP
        store_ip "$current_ip"

        # 更新 DNS 记录
        if update_dns_records "$current_ip"; then
            # 验证 DNS 更新
            if verify_dns_update "$current_ip"; then
                # 验证服务可访问性
                if verify_service_accessibility "$current_ip"; then
                    send_notification "IP 地址已更新并验证成功" "$stored_ip" "$current_ip"
                    log_success "IP 更新完成: $current_ip"
                else
                    send_notification "IP 地址更新后服务验证失败" "$stored_ip" "$current_ip"
                    log_error "服务可访问性验证失败"
                    return 1
                fi
            else
                send_notification "IP 地址更新后 DNS 验证失败" "$stored_ip" "$current_ip"
                log_error "DNS 验证失败"
                return 1
            fi
        else
            log_error "DNS 记录更新失败"
            return 1
        fi
    else
        log_info "IP 未发生变化，无需更新"
    fi
}

# 持续监控 IP 变化
monitor_ip_changes() {
    log_info "开始监控 IP 变化，检查间隔: ${CHECK_INTERVAL}秒"
    
    while true; do
        log_info "执行 IP 检查..."
        
        if perform_ip_update; then
            log_info "IP 检查完成"
        else
            log_error "IP 检查失败"
        fi
        
        log_info "等待 ${CHECK_INTERVAL}秒后进行下次检查..."
        sleep "$CHECK_INTERVAL"
    done
}

# 主函数
main() {
    case "$ACTION" in
        detect)
            if current_ip=$(detect_public_ip); then
                echo "$current_ip"
                log_success "当前公网 IP: $current_ip"
            else
                exit 1
            fi
            ;;
        update)
            perform_ip_update
            ;;
        monitor)
            monitor_ip_changes
            ;;
        setup)
            setup_dynamic_ip
            ;;
        *)
            log_error "未知操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main
