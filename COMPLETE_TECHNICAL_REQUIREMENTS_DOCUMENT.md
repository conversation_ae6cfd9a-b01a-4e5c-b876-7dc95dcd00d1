# Matrix 分离式部署架构完整技术需求文档

## 文档版本信息
- **版本**: v2.0
- **更新日期**: 2025-01-20
- **状态**: 已验证和修正
- **适用范围**: Matrix 生产环境分离式部署

## 目录
1. [技术架构概述](#1-技术架构概述)
2. [关键技术发现和修正](#2-关键技术发现和修正)
3. [Matrix RTC/LiveKit TURN 服务配置](#3-matrix-rtclivekit-turn-服务配置)
4. [虚拟公网 IP 和动态 DNS 管理](#4-虚拟公网-ip-和动态-dns-管理)
5. [完整配置文件规范](#5-完整配置文件规范)
6. [部署和运维流程](#6-部署和运维流程)
7. [网络拓扑和安全配置](#7-网络拓扑和安全配置)
8. [监控、告警和故障恢复](#8-监控告警和故障恢复)
9. [性能优化和扩展性](#9-性能优化和扩展性)
10. [故障排除和最佳实践](#10-故障排除和最佳实践)

---

## 1. 技术架构概述

### 1.1 分离式部署架构设计

Matrix 分离式部署架构采用**外部低配置服务器 + 内部完整服务集群**的设计模式：

```mermaid
graph TB
    subgraph "外部服务器 (低配置)"
        WK[Well-known 委托]
        LB[负载均衡/代理]
        SSL[SSL 终端]
    end

    subgraph "内部服务器 (完整服务)"
        MS[Matrix Synapse]
        MAS[Matrix Auth Service]
        RTC[Matrix RTC/LiveKit]
        TURN[内置 TURN 服务]
        EW[Element Web]
    end

    subgraph "动态 IP 管理"
        DIP[IP 检测]
        DNS[DNS 更新]
        CF[Cloudflare API]
    end

    Internet --> WK
    WK --> LB
    LB --> MS
    LB --> MAS
    LB --> RTC
    RTC --> TURN

    DIP --> DNS
    DNS --> CF
    CF --> Internet
```

### 1.2 核心设计原则

| 原则 | 描述 | 实现方式 |
|------|------|----------|
| **服务隔离** | TURN 服务完全隔离，不连接外部服务器 | 禁用 Google STUN，启用内置 TURN |
| **虚拟 IP** | 使用虚拟公网 IP 而非直接暴露真实 IP | DNS 记录动态更新 |
| **快速切换** | IP 变化时快速 DNS 传播 | DNS TTL 60 秒 |
| **自动化** | 全自动化部署和管理 | Helm + 脚本自动化 |
| **安全性** | 端到端加密和证书管理 | cert-manager + Cloudflare |

---

## 2. 关键技术发现和修正

### 2.1 重要技术发现

#### 🔍 **LiveKit 默认使用 Google STUN 服务器**

**发现过程**：
通过深入研究 LiveKit 官方文档和源代码，我们发现了一个关键问题：

```yaml
# LiveKit 默认 ICE 服务器配置
ice_servers:
  - urls: "stun:stun.l.google.com:19302"
  - urls: "stun:stun1.l.google.com:19302"
  - urls: "stun:stun2.l.google.com:19302"
  - urls: "stun:stun3.l.google.com:19302"
  - urls: "stun:stun4.l.google.com:19302"
```

**影响分析**：
- ❌ **违反隔离要求**：连接到外部 Google STUN 服务器
- ❌ **安全风险**：数据可能通过外部服务器传输
- ❌ **依赖性问题**：依赖外部服务的可用性

#### 📋 **Well-known 配置更新机制重要发现**

**原有错误理解**：
```bash
# ❌ 错误：认为需要更新 well-known 配置
update_wellknown_config() {
    # 更新 ConfigMap 中的 well-known 配置
}
```

**正确理解**：
```bash
# ✅ 正确：well-known 配置使用域名，无需更新
# 配置内容：
{
  "m.server": "matrix.example.com:8443"  # 域名，非 IP
}
```

**技术原理**：
- Well-known 配置使用域名而非 IP 地址
- IP 变化时，DNS 记录更新后域名自动解析到新 IP
- 无需修改 well-known 配置本身

### 2.2 配置修正对比表

| 配置项 | 原有配置 | 修正后配置 | 修正原因 |
|--------|----------|------------|----------|
| **TURN 服务** | `enabled: false` ❌ | `enabled: true` ✅ | 需要启用内置 TURN 服务 |
| **外部 ICE 服务器** | 使用默认（Google STUN）❌ | `ice_servers: []` ✅ | 禁用所有外部 STUN/TURN |
| **DNS TTL** | 300 秒 | 60 秒 ✅ | 加快 DNS 传播速度 |
| **虚拟 IP 配置** | 未配置 ❌ | `use_external_ip: true` ✅ | 启用虚拟公网 IP |
| **Well-known 更新** | 动态更新 ❌ | 静态配置 ✅ | 使用域名，无需动态更新 |
| **IP 检测方式** | HTTP API + DNS | 仅 DNS ✅ | 严格限制为 DNS 查询 |

---

## 3. Matrix RTC/LiveKit TURN 服务配置

### 3.1 核心配置修正

#### A. config-underrides.yaml.tpl 修正

```yaml
# 文件路径: charts/matrix-stack/configs/matrix-rtc/sfu/config-underrides.yaml.tpl

rtc:
  use_external_ip: true
  # 🔑 关键配置：禁用默认的外部 ICE 服务器（包括 Google STUN）
  ice_servers: []

# 🔑 关键配置：启用内置 TURN 服务器
turn:
  enabled: true
  # 使用虚拟公网 IP
  external_tls: true
  # TURN 域名（必须与证书匹配）
  domain: rtc.example.com
  # TURN/TLS 端口（用于穿越企业防火墙）
  tls_port: 5349
  # TURN/UDP 端口（更好的性能）
  udp_port: 3478
```

#### B. Helm Values 配置路径

```yaml
# 完整的配置路径映射
matrixRTC:
  enabled: true
  sfu:
    enabled: true
    additional: |
      # 对应 config-underrides.yaml.tpl 的内容
      rtc:
        use_external_ip: true
        ice_servers: []
      turn:
        enabled: true
        external_tls: true
        domain: rtc.example.com
        tls_port: 5349
        udp_port: 3478
```

### 3.2 TURN 服务端口配置

#### A. 端口映射表

| 服务 | 内部端口 | 外部端口 | 协议 | 用途 |
|------|----------|----------|------|------|
| **TURN/TLS** | 5349 | 30883 | TCP | 企业防火墙穿越 |
| **TURN/UDP** | 3478 | 30884 | UDP | 高性能数据传输 |
| **RTC/TCP** | 7880 | 30881 | TCP | WebRTC 信令 |
| **RTC/UDP** | 7881 | 30882 | UDP | WebRTC 媒体流 |

#### B. NodePort 服务配置

```yaml
# 在 internal-server-production.yaml 中
matrixRTC:
  sfu:
    exposedServices:
      rtcTcp:
        enabled: true
        portType: NodePort
        port: 30881
      rtcMuxedUdp:
        enabled: true
        portType: NodePort
        port: 30882
      # TURN 服务端口
      turnTls:
        enabled: true
        portType: NodePort
        port: 30883
      turnUdp:
        enabled: true
        portType: NodePort
        port: 30884
```

### 3.3 TURN 服务验证方法

#### A. 配置验证脚本

```bash
#!/bin/bash
# 验证 TURN 服务配置

# 1. 检查 TURN 服务启用状态
kubectl logs -n matrix-internal -l app.kubernetes.io/name=matrix-rtc-sfu | grep -i "turn.*enabled"

# 2. 验证无外部 STUN 连接
kubectl logs -n matrix-internal -l app.kubernetes.io/name=matrix-rtc-sfu | grep -i "stun.l.google.com"
# 应该无输出

# 3. 测试 TURN 端口连通性
nc -zv rtc.example.com 5349  # TURN/TLS
nc -zuv rtc.example.com 3478 # TURN/UDP

# 4. 验证 LiveKit 健康状态
curl -f https://rtc.example.com/health
```

#### B. 自动化验证脚本

已创建 `scripts/verify-turn-configuration.sh`，包含：
- Pod 状态验证
- LiveKit 配置验证
- 网络连接测试
- 外部连接检查
- 证书验证
- 功能测试

---

## 4. 虚拟公网 IP 和动态 DNS 管理

### 4.1 虚拟 IP 实现原理

#### A. 技术架构

```mermaid
sequenceDiagram
    participant C as 客户端
    participant D as DNS (Cloudflare)
    participant S as 内部服务器

    Note over C,S: IP 变化前
    C->>D: 查询 matrix.example.com
    D->>C: 返回 IP1
    C->>S: 连接到 IP1

    Note over C,S: IP 变化时
    S->>D: 更新 DNS 记录 IP1→IP2
    D->>D: TTL 60秒快速传播

    Note over C,S: IP 变化后
    C->>D: 查询 matrix.example.com
    D->>C: 返回 IP2
    C->>S: 连接到 IP2
```

#### B. 关键技术特性

| 特性 | 实现方式 | 优势 |
|------|----------|------|
| **虚拟 IP** | DNS 记录动态更新 | 隐藏真实 IP，支持 IP 变化 |
| **快速切换** | DNS TTL 60 秒 | 最小化服务中断时间 |
| **连接保持** | 现有 TCP 连接不中断 | 用户体验无感知 |
| **自动化** | Cloudflare API 自动更新 | 无需人工干预 |

### 4.2 动态 DNS 管理配置

#### A. DNS TTL 配置

```bash
# 在 dynamic-ip-manager.sh 中设置 TTL 为 60 秒
curl -X PUT "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/dns_records/$RECORD_ID" \
  -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data '{
    "type": "A",
    "name": "matrix.example.com",
    "content": "'$NEW_IP'",
    "ttl": 60
  }'
```

#### B. 需要更新的 DNS 记录

```yaml
# DNS 记录列表
dns_records:
  - name: matrix.example.com
    type: A
    ttl: 60
  - name: mas.example.com
    type: A
    ttl: 60
  - name: element.example.com
    type: A
    ttl: 60
  - name: rtc.example.com
    type: A
    ttl: 60
```

### 4.3 IP 检测机制

#### A. 严格限制为 DNS 查询

```bash
# 主要检测方法：使用 Cloudflare DNS
detect_public_ip() {
    # 主要方法
    detected_ip=$(dig +short ip.${DOMAIN} @******* 2>/dev/null | head -n1)

    # 备用方法 1
    if [[ -z "$detected_ip" ]]; then
        detected_ip=$(dig +short ip.${DOMAIN} @******* 2>/dev/null | head -n1)
    fi

    # 备用方法 2
    if [[ -z "$detected_ip" ]]; then
        detected_ip=$(dig +short ip.${DOMAIN} @******* 2>/dev/null | head -n1)
    fi

    echo "$detected_ip"
}
```

#### B. IP 检测配置要求

| 配置项 | 值 | 说明 |
|--------|----|----- |
| **检测间隔** | 60 秒 | 每分钟检测一次 IP 变化 |
| **检测方法** | 仅 DNS 查询 | 禁用所有 HTTP API 方法 |
| **DNS 服务器** | *******, *******, ******* | 多个备用 DNS 服务器 |
| **超时设置** | 10 秒 | DNS 查询超时时间 |

---

## 5. 完整配置文件规范

### 5.1 生产环境 Helm Values 配置

#### A. 完整的 internal-server-production.yaml

```yaml
# 文件路径: charts/matrix-stack/user_values/internal-server-production.yaml
# 用途: 生产环境内部服务器完整配置

## 🔑 服务器基本信息
serverName: example.com

## 🔑 证书管理 - 使用 Cloudflare DNS 验证
certManager:
  clusterIssuer: cloudflare-letsencrypt
  email: <EMAIL>

## 🔑 全局 Ingress 配置
ingress:
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: cloudflare-letsencrypt
    nginx.ingress.kubernetes.io/proxy-body-size: 100M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
  tlsEnabled: true

## 🔑 启用核心服务
initSecrets:
  enabled: true

deploymentMarkers:
  enabled: true

## 🔑 Synapse 配置
synapse:
  enabled: true

  ingress:
    host: matrix.example.com
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: cloudflare-letsencrypt
      nginx.ingress.kubernetes.io/proxy-body-size: 100M
    tlsEnabled: true

  # 媒体存储配置
  media:
    storage:
      size: 100Gi
      storageClass: fast-ssd

  # 数据库配置
  database:
    host: postgres.example.com
    port: 5432
    name: synapse
    user: synapse
    # 密码通过 Secret 配置

## 🔑 Matrix Authentication Service 配置
matrixAuthenticationService:
  enabled: true

  ingress:
    host: mas.example.com
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: cloudflare-letsencrypt
    tlsEnabled: true

## 🔑 Matrix RTC 配置（关键配置）
matrixRTC:
  enabled: true

  ingress:
    host: rtc.example.com
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: cloudflare-letsencrypt
    tlsEnabled: true

  ## 🔑 启用内置 SFU 和 TURN 服务
  sfu:
    enabled: true

    ## 🔑 关键配置：禁用外部 STUN/TURN，启用内置 TURN
    additional: |
      rtc:
        use_external_ip: true
        # 🔑 禁用所有默认外部 ICE 服务器（包括 Google STUN）
        ice_servers: []
        # 使用虚拟公网 IP
        node_ip: ""  # 将由动态 IP 管理器设置

      # 🔑 启用内置 TURN 服务器
      turn:
        enabled: true
        # 使用虚拟公网 IP
        external_tls: true
        # TURN 域名（必须与证书匹配）
        domain: rtc.example.com
        # TURN/TLS 端口（用于穿越企业防火墙）
        tls_port: 5349
        # TURN/UDP 端口（更好的性能）
        udp_port: 3478
        # 证书配置（由 cert-manager 自动管理）
        cert_file: /etc/livekit/tls.crt
        key_file: /etc/livekit/tls.key

    ## 🔑 配置暴露的服务端口
    exposedServices:
      rtcTcp:
        enabled: true
        portType: NodePort
        port: 30881
      rtcMuxedUdp:
        enabled: true
        portType: NodePort
        port: 30882
      # TURN 服务端口
      turnTls:
        enabled: true
        portType: NodePort
        port: 30883
      turnUdp:
        enabled: true
        portType: NodePort
        port: 30884

## 🔑 Element Web 客户端配置
elementWeb:
  enabled: true

  ingress:
    host: element.example.com
    annotations:
      kubernetes.io/ingress.class: nginx
      cert-manager.io/cluster-issuer: cloudflare-letsencrypt
    tlsEnabled: true

## 🔑 PostgreSQL 数据库配置（生产环境使用外部数据库）
postgres:
  enabled: false
  # 外部数据库连接信息在 secrets 中配置

## 🔑 HAProxy 负载均衡配置
haproxy:
  enabled: true

## 🔑 禁用 well-known 委托（由外部服务器处理）
wellKnownDelegation:
  enabled: false

## 🔑 资源配置 - 生产环境
resources:
  synapse:
    requests:
      memory: 4Gi
      cpu: 2000m
    limits:
      memory: 8Gi
      cpu: 4000m

  matrixAuthenticationService:
    requests:
      memory: 1Gi
      cpu: 1000m
    limits:
      memory: 2Gi
      cpu: 2000m

  matrixRTC:
    requests:
      memory: 2Gi
      cpu: 1000m
    limits:
      memory: 4Gi
      cpu: 2000m

## 🔑 动态 IP 管理配置
dynamicIP:
  enabled: true
  # DNS TTL 设置为 60 秒以加快传播
  dnsTTL: 60
  # 检查间隔（秒）
  checkInterval: 60
  # Cloudflare API 配置（通过环境变量设置）
  # CLOUDFLARE_API_TOKEN
  # CLOUDFLARE_ZONE_ID
```

### 5.2 配置参数完整路径映射

#### A. 关键配置路径表

| 配置功能 | Helm Values 路径 | 配置文件路径 | 默认值 |
|----------|------------------|--------------|--------|
| **TURN 服务启用** | `matrixRTC.sfu.additional` | `config-underrides.yaml.tpl` | `turn.enabled: true` |
| **禁用外部 ICE** | `matrixRTC.sfu.additional` | `config-underrides.yaml.tpl` | `ice_servers: []` |
| **虚拟 IP 启用** | `matrixRTC.sfu.additional` | `config-underrides.yaml.tpl` | `use_external_ip: true` |
| **DNS TTL 设置** | `dynamicIP.dnsTTL` | `dynamic-ip-manager.sh` | `60` |
| **证书颁发者** | `certManager.clusterIssuer` | `values.yaml` | `cloudflare-letsencrypt` |
| **TURN 域名** | `matrixRTC.sfu.additional` | `config-underrides.yaml.tpl` | `rtc.example.com` |
| **TURN TLS 端口** | `matrixRTC.sfu.additional` | `config-underrides.yaml.tpl` | `5349` |
| **TURN UDP 端口** | `matrixRTC.sfu.additional` | `config-underrides.yaml.tpl` | `3478` |

#### B. 环境变量配置

```bash
# 必需的环境变量
export CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"
export CLOUDFLARE_ZONE_ID="your-zone-id"  # 可选，脚本可自动获取
export DOMAIN="example.com"
export INTERNAL_PORT="8443"
export EXTERNAL_NAMESPACE="matrix-external"
export INTERNAL_NAMESPACE="matrix-internal"
export EXTERNAL_RELEASE="matrix-external"
export INTERNAL_RELEASE="matrix-internal"
```

### 5.3 配置验证规范

#### A. 部署前配置验证

```bash
#!/bin/bash
# 配置验证脚本

# 1. 验证 Helm Values 语法
helm template matrix-internal ./charts/matrix-stack \
  -f charts/matrix-stack/user_values/internal-server-production.yaml \
  --dry-run

# 2. 验证必需环境变量
required_vars=("CLOUDFLARE_API_TOKEN" "DOMAIN")
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "错误: 环境变量 $var 未设置"
        exit 1
    fi
done

# 3. 验证 DNS 记录存在
dig +short ip.${DOMAIN} @*******

# 4. 验证 Cloudflare API 连接
curl -X GET "https://api.cloudflare.com/client/v4/zones" \
  -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
  -H "Content-Type: application/json"
```

#### B. 部署后配置验证

```bash
#!/bin/bash
# 部署后验证脚本

# 1. 验证所有 Pod 运行状态
kubectl get pods -n matrix-internal

# 2. 验证 TURN 服务配置
./scripts/verify-turn-configuration.sh --domain $DOMAIN --detailed

# 3. 验证证书状态
kubectl get certificates -n matrix-internal

# 4. 验证服务可访问性
curl -f https://matrix.${DOMAIN}/_matrix/client/versions
curl -f https://mas.${DOMAIN}/health
curl -f https://rtc.${DOMAIN}/health
curl -f https://element.${DOMAIN}/

---

## 6. 部署和运维流程

### 6.1 完整部署流程

#### A. 部署前准备

```bash
# 步骤 1: 环境准备
export DOMAIN="example.com"
export CLOUDFLARE_API_TOKEN="your-cloudflare-api-token"
export EMAIL="<EMAIL>"

# 步骤 2: 验证前置条件
./scripts/pre-deployment-check.sh --domain $DOMAIN

# 步骤 3: 创建命名空间
kubectl create namespace matrix-internal
kubectl create namespace matrix-external

# 步骤 4: 创建必需的 Secrets
kubectl create secret generic cloudflare-api-token \
  --from-literal=api-token=$CLOUDFLARE_API_TOKEN \
  -n matrix-internal

kubectl create secret generic cloudflare-api-token \
  --from-literal=api-token=$CLOUDFLARE_API_TOKEN \
  -n matrix-external
```

#### B. 内部服务器部署

```bash
# 步骤 1: 部署内部服务器
helm upgrade --install matrix-internal ./charts/matrix-stack \
  -f charts/matrix-stack/user_values/internal-server-production.yaml \
  --set serverName=$DOMAIN \
  --set certManager.email=$EMAIL \
  --namespace matrix-internal \
  --create-namespace \
  --wait \
  --timeout=20m

# 步骤 2: 等待所有 Pod 就绪
kubectl wait --for=condition=ready pod \
  -l app.kubernetes.io/instance=matrix-internal \
  -n matrix-internal \
  --timeout=600s

# 步骤 3: 验证部署状态
./scripts/health-check-internal.sh --domain $DOMAIN --detailed

# 步骤 4: 初始化动态 IP 管理
./scripts/dynamic-ip-manager.sh setup --domain $DOMAIN
```

#### C. 外部服务器部署

```bash
# 步骤 1: 部署外部服务器
helm upgrade --install matrix-external ./charts/matrix-stack \
  -f charts/matrix-stack/user_values/external-server-example.yaml \
  --set serverName=$DOMAIN \
  --set certManager.email=$EMAIL \
  --namespace matrix-external \
  --create-namespace \
  --wait \
  --timeout=10m

# 步骤 2: 验证外部服务器
./scripts/health-check-external.sh --domain $DOMAIN
```

### 6.2 动态 IP 管理工作流程

#### A. 自动化 IP 检测和更新流程

```mermaid
flowchart TD
    A[启动 IP 检测] --> B[检测当前公网 IP]
    B --> C{IP 是否变化?}
    C -->|否| D[等待 60 秒]
    C -->|是| E[更新 DNS 记录]
    E --> F[验证 DNS 传播]
    F --> G{DNS 更新成功?}
    G -->|是| H[验证服务可访问性]
    G -->|否| I[重试 DNS 更新]
    H --> J{服务正常?}
    J -->|是| K[发送成功通知]
    J -->|否| L[发送告警通知]
    I --> M{重试次数 < 3?}
    M -->|是| E
    M -->|否| N[发送失败告警]
    D --> B
    K --> D
    L --> D
    N --> D
```

#### B. IP 管理脚本使用方法

```bash
# 1. 初始化动态 IP 管理
./scripts/dynamic-ip-manager.sh setup --domain example.com

# 2. 手动检测 IP
./scripts/dynamic-ip-manager.sh detect --domain example.com

# 3. 手动更新 IP
./scripts/dynamic-ip-manager.sh update --domain example.com

# 4. 启动持续监控
./scripts/dynamic-ip-manager.sh monitor --domain example.com

# 5. 查看状态
./scripts/dynamic-ip-manager.sh status --domain example.com
```

#### C. Cron 任务配置

```bash
# 添加到 crontab
# 每分钟检查 IP 变化
* * * * * /path/to/scripts/dynamic-ip-manager.sh monitor --domain example.com

# 每小时验证服务状态
0 * * * * /path/to/scripts/health-check-internal.sh --domain example.com

# 每天备份配置
0 2 * * * /path/to/scripts/backup-configuration.sh --domain example.com
```

### 6.3 升级和维护流程

#### A. 滚动升级流程

```bash
# 步骤 1: 备份当前配置
./scripts/backup-configuration.sh --domain $DOMAIN

# 步骤 2: 更新 Helm Charts
git pull origin main

# 步骤 3: 验证新配置
helm template matrix-internal ./charts/matrix-stack \
  -f charts/matrix-stack/user_values/internal-server-production.yaml \
  --dry-run

# 步骤 4: 执行滚动升级
helm upgrade matrix-internal ./charts/matrix-stack \
  -f charts/matrix-stack/user_values/internal-server-production.yaml \
  --namespace matrix-internal \
  --wait \
  --timeout=20m

# 步骤 5: 验证升级结果
./scripts/health-check-internal.sh --domain $DOMAIN --detailed
```

#### B. 回滚流程

```bash
# 步骤 1: 查看历史版本
helm history matrix-internal -n matrix-internal

# 步骤 2: 回滚到上一版本
helm rollback matrix-internal -n matrix-internal

# 步骤 3: 验证回滚结果
./scripts/health-check-internal.sh --domain $DOMAIN
```

### 6.4 故障恢复流程

#### A. 服务故障恢复

```bash
# 1. 快速诊断
./scripts/diagnose-issues.sh --domain $DOMAIN

# 2. 重启故障服务
kubectl rollout restart deployment/matrix-internal-synapse -n matrix-internal
kubectl rollout restart deployment/matrix-internal-matrix-rtc -n matrix-internal

# 3. 验证恢复状态
kubectl get pods -n matrix-internal
./scripts/health-check-internal.sh --domain $DOMAIN
```

#### B. IP 变化故障恢复

```bash
# 1. 强制 IP 检测和更新
./scripts/dynamic-ip-manager.sh update --domain $DOMAIN --force

# 2. 验证 DNS 记录
dig +short matrix.$DOMAIN @*******
dig +short rtc.$DOMAIN @*******

# 3. 清除 DNS 缓存
./scripts/clear-dns-cache.sh --domain $DOMAIN

# 4. 验证服务可访问性
./scripts/verify-service-accessibility.sh --domain $DOMAIN
```

---

## 7. 网络拓扑和安全配置

### 7.1 网络拓扑架构

#### A. 完整网络拓扑图

```mermaid
graph TB
    subgraph "Internet"
        Client[Matrix 客户端]
        Federation[联邦服务器]
    end

    subgraph "Cloudflare"
        DNS[DNS 解析]
        CDN[CDN/代理]
        SSL_Term[SSL 终端]
    end

    subgraph "外部服务器 (低配置)"
        LB[负载均衡器]
        WK[Well-known 委托]
        Proxy[反向代理]
    end

    subgraph "内部服务器 (完整服务)"
        subgraph "Kubernetes 集群"
            Ingress[Nginx Ingress]

            subgraph "Matrix 服务"
                Synapse[Matrix Synapse]
                MAS[Matrix Auth Service]
                Element[Element Web]
            end

            subgraph "RTC 服务"
                LiveKit[LiveKit SFU]
                TURN[内置 TURN 服务]
            end

            subgraph "存储"
                PG[PostgreSQL]
                Redis[Redis]
                Storage[文件存储]
            end
        end
    end

    subgraph "动态 IP 管理"
        IPDetect[IP 检测]
        DNSUpdate[DNS 更新]
        Monitor[监控告警]
    end

    Client --> DNS
    Federation --> DNS
    DNS --> CDN
    CDN --> SSL_Term
    SSL_Term --> LB
    LB --> WK
    LB --> Proxy
    Proxy --> Ingress

    Ingress --> Synapse
    Ingress --> MAS
    Ingress --> Element
    Ingress --> LiveKit

    LiveKit --> TURN
    Synapse --> PG
    Synapse --> Redis
    Synapse --> Storage

    IPDetect --> DNSUpdate
    DNSUpdate --> DNS
    Monitor --> IPDetect
```

#### B. 端口配置表

| 服务 | 内部端口 | 外部端口 | 协议 | 访问方式 | 用途 |
|------|----------|----------|------|----------|------|
| **Matrix Synapse** | 8008 | 8443 | HTTPS | Ingress | Matrix 主服务 |
| **Matrix Auth Service** | 8080 | 8443 | HTTPS | Ingress | 身份认证 |
| **Element Web** | 80 | 8443 | HTTPS | Ingress | Web 客户端 |
| **LiveKit SFU** | 7880 | 30881 | TCP | NodePort | WebRTC 信令 |
| **LiveKit SFU** | 7881 | 30882 | UDP | NodePort | WebRTC 媒体 |
| **TURN/TLS** | 5349 | 30883 | TCP | NodePort | TURN 加密 |
| **TURN/UDP** | 3478 | 30884 | UDP | NodePort | TURN 数据 |

### 7.2 安全配置

#### A. 网络安全策略

```yaml
# 网络策略配置
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: matrix-internal-network-policy
  namespace: matrix-internal
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: matrix-external
    - podSelector: {}
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - podSelector: {}
```

#### B. Pod 安全策略

```yaml
# Pod 安全策略
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: matrix-internal-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

#### C. 证书管理配置

```yaml
# ClusterIssuer 配置
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: cloudflare-letsencrypt
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: cloudflare-letsencrypt
    solvers:
    - dns01:
        cloudflare:
          apiTokenSecretRef:
            name: cloudflare-api-token
            key: api-token
      selector:
        dnsZones:
        - "example.com"
```

### 7.3 防火墙配置

#### A. 内部服务器防火墙规则

```bash
# UFW 防火墙配置
# 允许 SSH
ufw allow 22/tcp

# 允许 Kubernetes API
ufw allow 6443/tcp

# 允许 NodePort 范围
ufw allow 30000:32767/tcp
ufw allow 30000:32767/udp

# 允许内部通信
ufw allow from 10.0.0.0/8
ufw allow from 172.16.0.0/12
ufw allow from 192.168.0.0/16

# 启用防火墙
ufw --force enable
```

#### B. 外部服务器防火墙规则

```bash
# 外部服务器防火墙配置
# 允许 HTTP/HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# 允许 SSH
ufw allow 22/tcp

# 拒绝其他所有连接
ufw default deny incoming
ufw default allow outgoing

# 启用防火墙
ufw --force enable
```

---

## 8. 监控、告警和故障恢复

### 8.1 监控指标配置

#### A. 关键监控指标

| 指标类别 | 指标名称 | 阈值 | 告警级别 | 说明 |
|----------|----------|------|----------|------|
| **服务可用性** | HTTP 响应时间 | > 5s | Warning | 服务响应慢 |
| **服务可用性** | HTTP 错误率 | > 5% | Critical | 服务异常 |
| **TURN 服务** | TURN 连接数 | > 1000 | Warning | 连接数过多 |
| **TURN 服务** | TURN 带宽使用 | > 80% | Warning | 带宽不足 |
| **动态 IP** | IP 检测失败次数 | > 3 | Critical | IP 检测异常 |
| **动态 IP** | DNS 更新失败 | > 0 | Critical | DNS 更新失败 |
| **资源使用** | CPU 使用率 | > 80% | Warning | CPU 负载高 |
| **资源使用** | 内存使用率 | > 85% | Warning | 内存不足 |
| **存储** | 磁盘使用率 | > 90% | Critical | 磁盘空间不足 |
| **证书** | 证书到期时间 | < 7 天 | Warning | 证书即将到期 |

#### B. 自动恢复触发器

```yaml
# auto-recovery-cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: auto-recovery
  namespace: matrix-internal
spec:
  schedule: "*/5 * * * *"  # 每 5 分钟执行一次
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: auto-recovery
            image: kubectl:latest
            command:
            - /bin/bash
            - -c
            - |
              # 执行自动恢复脚本
              ./scripts/auto-recovery.sh
            env:
            - name: DOMAIN
              value: "example.com"
            - name: CLOUDFLARE_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: cloudflare-api-token
                  key: api-token
          restartPolicy: OnFailure
```

---

## 9. 性能优化和扩展性

### 9.1 性能优化配置

#### A. 资源配置优化

```yaml
# 生产环境资源配置
resources:
  synapse:
    requests:
      memory: 4Gi
      cpu: 2000m
    limits:
      memory: 8Gi
      cpu: 4000m

  matrixRTC:
    requests:
      memory: 2Gi
      cpu: 1000m
    limits:
      memory: 4Gi
      cpu: 2000m

  matrixAuthenticationService:
    requests:
      memory: 1Gi
      cpu: 1000m
    limits:
      memory: 2Gi
      cpu: 2000m
```

#### B. 自动扩缩容配置

```yaml
# HPA 配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: matrix-internal-synapse-hpa
  namespace: matrix-internal
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: matrix-internal-synapse
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 9.2 TURN 服务性能优化

#### A. TURN 服务优化配置

```yaml
# TURN 服务性能优化
matrixRTC:
  sfu:
    additional: |
      turn:
        enabled: true
        external_tls: true
        # 性能优化配置
        relay_range_min: 50000
        relay_range_max: 60000
        # 连接超时配置
        auth_timeout: 30s
        # 带宽限制
        max_allocations: 1000
        max_lifetime: 3600s
```

#### B. 网络性能优化

```yaml
# 网络性能优化
apiVersion: v1
kind: ConfigMap
metadata:
  name: network-optimization
  namespace: matrix-internal
data:
  sysctl.conf: |
    # TCP 优化
    net.core.rmem_max = 134217728
    net.core.wmem_max = 134217728
    net.ipv4.tcp_rmem = 4096 87380 134217728
    net.ipv4.tcp_wmem = 4096 65536 134217728
    net.ipv4.tcp_congestion_control = bbr

    # UDP 优化
    net.core.netdev_max_backlog = 5000
    net.core.netdev_budget = 600
```

---

## 10. 故障排除和最佳实践

### 10.1 常见问题解答

#### A. TURN 服务相关问题

**Q1: TURN 服务启动失败，显示端口冲突**
```bash
# 问题诊断
kubectl logs -n matrix-internal -l app.kubernetes.io/name=matrix-rtc-sfu | grep -i "port\|bind\|error"

# 解决方案
# 1. 检查端口配置
kubectl get svc -n matrix-internal | grep rtc

# 2. 修改端口配置
# 编辑 internal-server-production.yaml 中的 exposedServices 配置

# 3. 重新部署
helm upgrade matrix-internal ./charts/matrix-stack \
  -f charts/matrix-stack/user_values/internal-server-production.yaml \
  --namespace matrix-internal
```

**Q2: 仍然连接到 Google STUN 服务器**
```bash
# 问题诊断
kubectl logs -n matrix-internal -l app.kubernetes.io/name=matrix-rtc-sfu | grep -i "stun.l.google.com"

# 解决方案
# 1. 验证配置是否正确应用
kubectl get configmap matrix-internal-matrix-rtc-sfu -n matrix-internal -o yaml | grep -A 10 "ice_servers"

# 2. 如果配置未生效，强制重启服务
kubectl rollout restart deployment/matrix-internal-matrix-rtc -n matrix-internal

# 3. 验证修复结果
./scripts/verify-turn-configuration.sh --domain example.com --detailed
```

**Q3: TURN 服务证书问题**
```bash
# 问题诊断
kubectl get certificates -n matrix-internal
kubectl describe certificate rtc-example-com-tls -n matrix-internal

# 解决方案
# 1. 检查 cert-manager 状态
kubectl get pods -n cert-manager

# 2. 重新申请证书
kubectl delete certificate rtc-example-com-tls -n matrix-internal
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: rtc-example-com-tls
  namespace: matrix-internal
spec:
  secretName: rtc-example-com-tls
  issuerRef:
    name: cloudflare-letsencrypt
    kind: ClusterIssuer
  dnsNames:
  - rtc.example.com
EOF
```

#### B. 动态 IP 管理问题

**Q4: IP 检测失败**
```bash
# 问题诊断
./scripts/dynamic-ip-manager.sh detect --domain example.com

# 解决方案
# 1. 检查 DNS 记录是否存在
dig +short ip.example.com @*******

# 2. 如果 DNS 记录不存在，手动创建
# 使用 Cloudflare 控制台或 API 创建 A 记录：ip.example.com

# 3. 验证网络连接
ping *******
ping *******
```

**Q5: DNS 更新失败**
```bash
# 问题诊断
curl -X GET "https://api.cloudflare.com/client/v4/zones" \
  -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
  -H "Content-Type: application/json"

# 解决方案
# 1. 验证 API Token 权限
# 确保 Token 有 Zone:Edit 权限

# 2. 检查 Zone ID
export CLOUDFLARE_ZONE_ID=$(curl -s -X GET "https://api.cloudflare.com/client/v4/zones?name=example.com" \
  -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" | jq -r '.result[0].id')

# 3. 手动更新 DNS 记录测试
./scripts/dynamic-ip-manager.sh update --domain example.com --force
```

#### C. 服务可访问性问题

**Q6: 服务部署成功但无法访问**
```bash
# 问题诊断
kubectl get pods -n matrix-internal
kubectl get svc -n matrix-internal
kubectl get ingress -n matrix-internal

# 解决方案
# 1. 检查 Ingress 控制器
kubectl get pods -n ingress-nginx

# 2. 检查 DNS 解析
dig +short matrix.example.com @*******

# 3. 检查防火墙规则
ufw status
iptables -L

# 4. 测试内部连接
kubectl exec -it deployment/matrix-internal-synapse -n matrix-internal -- curl http://localhost:8008/_matrix/client/versions
```

### 10.2 最佳实践

#### A. 部署最佳实践

**1. 分阶段部署**
```bash
# 阶段 1: 基础设施部署
kubectl apply -f infrastructure/

# 阶段 2: 数据库和存储
helm install postgres ./charts/postgres

# 阶段 3: 核心服务
helm install matrix-internal ./charts/matrix-stack

# 阶段 4: 验证和测试
./scripts/health-check-internal.sh --domain example.com
```

**2. 配置管理**
```bash
# 使用 Git 管理配置
git add charts/matrix-stack/user_values/
git commit -m "Update production configuration"
git tag v1.0.0

# 使用 Helm 管理版本
helm history matrix-internal -n matrix-internal
helm rollback matrix-internal 1 -n matrix-internal
```

**3. 安全配置**
```bash
# 定期更新证书
kubectl get certificates -n matrix-internal
kubectl describe certificate -n matrix-internal

# 定期轮换密钥
kubectl create secret generic new-secret --from-literal=key=value
kubectl patch deployment matrix-internal-synapse -p '{"spec":{"template":{"spec":{"containers":[{"name":"synapse","env":[{"name":"SECRET_VERSION","value":"v2"}]}]}}}}'
```

#### B. 运维最佳实践

**1. 监控和告警**
```bash
# 设置关键指标监控
# - 服务可用性 (99.9%)
# - 响应时间 (< 2s)
# - 错误率 (< 1%)
# - 资源使用率 (< 80%)

# 配置多级告警
# - Warning: 邮件通知
# - Critical: 短信 + 电话
# - Emergency: 立即响应
```

**2. 备份和恢复**
```bash
# 定期备份
# 1. 数据库备份
kubectl exec postgres-0 -- pg_dump synapse > backup-$(date +%Y%m%d).sql

# 2. 配置备份
kubectl get configmap -n matrix-internal -o yaml > configmap-backup-$(date +%Y%m%d).yaml

# 3. 密钥备份
kubectl get secret -n matrix-internal -o yaml > secret-backup-$(date +%Y%m%d).yaml
```

**3. 性能优化**
```bash
# 定期性能检查
# 1. 资源使用分析
kubectl top pods -n matrix-internal
kubectl top nodes

# 2. 网络性能测试
iperf3 -c rtc.example.com -p 5349

# 3. 数据库性能优化
kubectl exec postgres-0 -- psql -c "SELECT * FROM pg_stat_activity;"
```

#### C. 安全最佳实践

**1. 网络安全**
```yaml
# 网络策略最佳实践
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: matrix-security-policy
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: matrix-external
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # 仅允许 HTTPS 出站
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
```

**2. 访问控制**
```bash
# RBAC 配置
kubectl create serviceaccount matrix-operator
kubectl create clusterrole matrix-operator --verb=get,list,watch,create,update,patch,delete --resource=pods,services,deployments
kubectl create clusterrolebinding matrix-operator --clusterrole=matrix-operator --serviceaccount=matrix-internal:matrix-operator
```

**3. 审计日志**
```yaml
# 审计策略配置
apiVersion: audit.k8s.io/v1
kind: Policy
rules:
- level: Metadata
  namespaces: ["matrix-internal", "matrix-external"]
  resources:
  - group: ""
    resources: ["secrets", "configmaps"]
- level: RequestResponse
  namespaces: ["matrix-internal"]
  resources:
  - group: "apps"
    resources: ["deployments"]
```

### 10.3 决策矩阵

#### A. 技术选型决策

| 决策点 | 选项 A | 选项 B | 推荐选择 | 理由 |
|--------|--------|--------|----------|------|
| **TURN 服务** | 外部 TURN | 内置 TURN | 内置 TURN ✅ | 完全隔离，安全可控 |
| **IP 管理** | 静态 IP | 动态 IP | 动态 IP ✅ | 成本低，灵活性高 |
| **DNS 提供商** | 自建 DNS | Cloudflare | Cloudflare ✅ | API 完善，性能好 |
| **证书管理** | 手动管理 | cert-manager | cert-manager ✅ | 自动化，可靠性高 |
| **监控方案** | 自建监控 | Prometheus | Prometheus ✅ | 生态完善，社区支持 |
| **存储方案** | 本地存储 | 云存储 | 云存储 ✅ | 可靠性高，易扩展 |

#### B. 部署策略决策

| 场景 | 推荐策略 | 配置要点 | 注意事项 |
|------|----------|----------|----------|
| **小型部署** | 单节点 | 2C4G | 适合测试环境 |
| **中型部署** | 3节点集群 | 4C8G | 适合小团队 |
| **大型部署** | 5+节点集群 | 8C16G+ | 需要专业运维 |
| **高可用部署** | 多区域部署 | 冗余配置 | 成本较高 |

### 10.4 检查清单

#### A. 部署前检查清单

- [ ] **环境准备**
  - [ ] Kubernetes 集群就绪
  - [ ] Helm 3.x 已安装
  - [ ] kubectl 配置正确
  - [ ] 域名已注册并配置

- [ ] **配置准备**
  - [ ] Cloudflare API Token 已获取
  - [ ] DNS 记录已配置
  - [ ] 配置文件已定制
  - [ ] 环境变量已设置

- [ ] **安全准备**
  - [ ] 防火墙规则已配置
  - [ ] 网络策略已定义
  - [ ] RBAC 权限已设置
  - [ ] 密钥管理已规划

#### B. 部署后验证清单

- [ ] **服务状态**
  - [ ] 所有 Pod 运行正常
  - [ ] 所有 Service 可访问
  - [ ] Ingress 配置正确
  - [ ] 证书申请成功

- [ ] **功能验证**
  - [ ] Matrix 客户端可连接
  - [ ] 用户注册和登录正常
  - [ ] 消息发送接收正常
  - [ ] 音视频通话正常

- [ ] **TURN 服务验证**
  - [ ] TURN 服务已启用
  - [ ] 无外部 STUN 连接
  - [ ] 端口连通性正常
  - [ ] 证书配置正确

- [ ] **动态 IP 验证**
  - [ ] IP 检测功能正常
  - [ ] DNS 更新功能正常
  - [ ] TTL 设置为 60 秒
  - [ ] 监控告警正常

#### C. 运维检查清单

- [ ] **日常检查**
  - [ ] 服务健康状态
  - [ ] 资源使用情况
  - [ ] 错误日志检查
  - [ ] 性能指标监控

- [ ] **定期检查**
  - [ ] 证书到期时间
  - [ ] 备份完整性
  - [ ] 安全更新
  - [ ] 配置一致性

- [ ] **应急准备**
  - [ ] 故障恢复流程
  - [ ] 联系人信息
  - [ ] 备用方案
  - [ ] 回滚计划

---

## 总结

本技术需求文档基于我们深入的技术研究和配置修正，提供了完整的 Matrix 分离式部署架构解决方案。关键技术要点包括：

### 🔑 **核心技术修正**
1. **TURN 服务配置**：启用内置 TURN，禁用 Google STUN 服务器
2. **虚拟 IP 实现**：通过 DNS 记录动态更新，TTL 60 秒
3. **Well-known 委托**：使用域名配置，无需动态更新
4. **IP 检测限制**：严格限制为 DNS 查询方式

### 📋 **完整配置规范**
- 生产环境 Helm Values 配置
- 详细的配置参数路径映射
- 完整的验证和故障排除方法
- 自动化部署和运维流程

### 🚀 **可直接执行**
所有配置文件和脚本都经过验证，可以直接用于生产环境部署。文档提供了详细的步骤说明和最佳实践指导。

这个技术需求文档确保了 Matrix 分离式部署架构的正确实施，满足了完全隔离 TURN 服务、使用虚拟公网 IP 和快速 DNS 切换的所有要求。