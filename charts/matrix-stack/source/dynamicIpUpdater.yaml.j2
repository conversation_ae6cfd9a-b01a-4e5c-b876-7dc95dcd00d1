{{- /*
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
*/ -}}

{{- $root := .root -}}
{{- with required "dynamicIpUpdater.yaml.j2 missing context" .context -}}

{{- if .enabled -}}

# ConfigMap to store current IP address
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-config
  namespace: {{ $root.Release.Namespace }}
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}
data:
  current-ip: ""
  last-update: ""

---

# CronJob for IP detection and update
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  namespace: {{ $root.Release.Namespace }}
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}
spec:
  schedule: {{ .ipDetection.schedule | quote }}
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 12 }}
        spec:
          restartPolicy: OnFailure
          serviceAccountName: {{ $root.Release.Name }}-dynamic-ip-updater
          containers:
          - name: ip-updater
            image: {{ include "element-io.ess-library.image.name" (dict "root" $root "context" $root.Values.matrixTools) }}
            imagePullPolicy: {{ include "element-io.ess-library.image.pullPolicy" (dict "root" $root "context" $root.Values.matrixTools) }}
            command:
            - /bin/bash
            - -c
            - |
              set -euo pipefail
              
              # IP 检测函数
              detect_ip() {
                local method="$1"
                echo "Detecting IP using: $method" >&2
                eval "$method" 2>/dev/null || echo ""
              }
              
              # 获取当前存储的 IP
              CURRENT_IP=$(kubectl get configmap {{ $root.Release.Name }}-dynamic-ip-config -o jsonpath='{.data.current-ip}' 2>/dev/null || echo "")
              echo "Current stored IP: $CURRENT_IP"
              
              # 检测新 IP
              NEW_IP=""
              
              # 尝试主要方法
              NEW_IP=$(detect_ip "{{ .ipDetection.primary.command }}")
              
              # 如果主要方法失败，尝试备用方法
              if [[ -z "$NEW_IP" ]]; then
                {{- range .ipDetection.fallback }}
                if [[ -z "$NEW_IP" ]]; then
                  NEW_IP=$(detect_ip "{{ . }}")
                fi
                {{- end }}
              fi
              
              if [[ -z "$NEW_IP" ]]; then
                echo "ERROR: Failed to detect IP address using all methods"
                exit 1
              fi
              
              echo "Detected IP: $NEW_IP"
              
              # 检查 IP 是否发生变化
              if [[ "$NEW_IP" != "$CURRENT_IP" ]]; then
                echo "IP changed from $CURRENT_IP to $NEW_IP"
                
                # 更新 ConfigMap
                kubectl patch configmap {{ $root.Release.Name }}-dynamic-ip-config --patch "{
                  \"data\": {
                    \"current-ip\": \"$NEW_IP\",
                    \"last-update\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
                  }
                }"
                
                # 更新 well-known 配置
                {{- $internalServer := .internalServer }}
                SERVER_CONFIG="{\"m.server\": \"{{ $internalServer.subdomains.matrix }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}\"}"
                CLIENT_CONFIG="{
                  \"m.homeserver\": {
                    \"base_url\": \"https://{{ $internalServer.subdomains.matrix }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}\"
                  },
                  \"org.matrix.msc2965.authentication\": {
                    \"issuer\": \"https://{{ $internalServer.subdomains.mas }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}/\",
                    \"account\": \"https://{{ $internalServer.subdomains.mas }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}/account\"
                  },
                  \"org.matrix.msc4143.rtc_foci\": [
                    {
                      \"type\": \"livekit\",
                      \"livekit_service_url\": \"https://{{ $internalServer.subdomains.rtc }}.{{ $internalServer.baseHost }}:{{ $internalServer.port }}\"
                    }
                  ]
                }"
                
                # 更新 well-known ConfigMap
                kubectl patch configmap {{ $root.Release.Name }}-well-known-haproxy --patch "{
                  \"data\": {
                    \"server\": \"$SERVER_CONFIG\",
                    \"client\": \"$CLIENT_CONFIG\"
                  }
                }" || echo "Warning: Failed to update well-known config"
                
                echo "IP update completed successfully"
              else
                echo "IP unchanged, no update needed"
              fi
            env:
            - name: KUBECONFIG
              value: /var/run/secrets/kubernetes.io/serviceaccount
            volumeMounts:
            - name: service-account-token
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true
          volumes:
          - name: service-account-token
            projected:
              sources:
              - serviceAccountToken:
                  path: token
              - configMap:
                  name: kube-root-ca.crt
                  items:
                  - key: ca.crt
                    path: ca.crt
              - downwardAPI:
                  items:
                  - path: namespace
                    fieldRef:
                      fieldPath: metadata.namespace

---

# ServiceAccount for the IP updater
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  namespace: {{ $root.Release.Namespace }}
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}

---

# ClusterRole for the IP updater
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "patch", "update"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]

---

# ClusterRoleBinding for the IP updater
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  labels:
    {{- include "element-io.dynamic-ip-updater.labels" (dict "root" $root "context" .) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ $root.Release.Name }}-dynamic-ip-updater
subjects:
- kind: ServiceAccount
  name: {{ $root.Release.Name }}-dynamic-ip-updater
  namespace: {{ $root.Release.Namespace }}

{{- end -}}
{{- end -}}
